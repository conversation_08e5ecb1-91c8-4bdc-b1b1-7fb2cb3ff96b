#!/usr/bin/env python3
"""
Main entry point for AreTomo3 GUI application.
"""

# Initialize Qt and matplotlib backend before any imports that might use them
import os
import sys

# Initialize Qt backend before any other imports
from aretomo3_gui.qt_backend_init import initialize_qt_backend

# Initialize Qt backend and matplotlib
initialize_qt_backend()

# Verify PyQt6 is working
try:
    from PyQt6.QtWidgets import QApplication
    if QApplication.instance() is None:
        app = QApplication([])
except ImportError as e:
    print(f"Error: Failed to initialize PyQt6: {e}")
    sys.exit(1)

import argparse
import logging
import signal
import sys
from pathlib import Path


def setup_logging(debug=False):
    """Set up logging configuration."""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)],
    )


def check_dependencies(logger=None):
    """Check if all required dependencies are available."""
    missing_deps = []

    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")

    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")

    try:
        import matplotlib
    except ImportError:
        missing_deps.append("matplotlib")

    try:
        import mrcfile
    except ImportError:
        missing_deps.append("mrcfile")

    if missing_deps:
        if logger:
            logger.info(f"❌ Missing required dependencies: {', '.join(missing_deps)}")
            logger.info("Please install them with: pip install aretomo3-gui")
        else:
            print(f"❌ Missing required dependencies: {', '.join(missing_deps)}")
            print("Please install them with: pip install aretomo3-gui")
        return False

    return True


def check_eer_support(logger=None):
    """Check EER support status - EER support has been removed."""
    if logger:
        logger.info("ℹ️  EER support has been removed from AT3Gui")
        logger.info("   Please convert EER files to MRC format using external tools")
    else:
        print("ℹ️  EER support has been removed from AT3Gui")
        print("   Please convert EER files to MRC format using external tools")


def main():
    """Main entry point for the application."""
    parser = argparse.ArgumentParser(description="AreTomo3 GUI")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument(
        "--check-eer", action="store_true", help="Check EER support and exit"
    )
    parser.add_argument("--version", action="version", version="AreTomo3 GUI v3.0.0")
    parser.add_argument("files", nargs="*", help="Files to open on startup")

    args = parser.parse_args()

    # Set up logging
    setup_logging(args.debug)
    logger = logging.getLogger(__name__)

    logger.info("Starting AreTomo3 GUI...")

    # Check dependencies
    if not check_dependencies(logger):
        sys.exit(1)

    # Check EER support if requested
    if args.check_eer:
        check_eer_support(logger)
        return

    try:
        # Import here to avoid circular imports
        from aretomo3_gui.gui.rich_main_window import RichAreTomoGUI as AreTomoGUI
        from PyQt6.QtWidgets import QApplication

        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # Create main window
        window = AreTomoGUI()
        
        # Position window in center of screen
        screen = QApplication.primaryScreen().availableGeometry()
        window.resize(int(screen.width() * 0.8), int(screen.height() * 0.8))
        window.move(
            (screen.width() - window.width()) // 2,
            (screen.height() - window.height()) // 2
        )
        
        # Set up signal handlers for graceful shutdown
        def signal_handler(signum, frame):
            """Execute signal_handler operation."""
            logger.info(f"Received signal {signum}, shutting down gracefully...")
            try:
                window.close()
            except Exception:
                pass
            QApplication.instance().quit()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Show the window
        window.show()
        window.raise_()
        window.activateWindow()
        logger.info("Displaying main window...")

        # Run the event loop
        logger.info("Starting Qt event loop...")
        sys.exit(app.exec())

    except ImportError as e:
        logger.error(f"Failed to import GUI components: {e}")
        logger.info("❌ GUI components could not be loaded.")
        logger.info("Please ensure PyQt6 is properly installed.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        logger.info(f"❌ An unexpected error occurred: {e}")
        sys.exit(1)


# Expose AreTomoGUI for import (lazy loading to avoid Qt issues)
def get_aretomo_gui():
    """Get AreTomoGUI class with lazy loading."""
    try:
        from aretomo3_gui.gui.rich_main_window import RichAreTomoGUI as AreTomoGUI
        return AreTomoGUI
    except ImportError:
        return None


AreTomoGUI = get_aretomo_gui

__all__ = ["main", "AreTomoGUI"]

if __name__ == "__main__":
    main()
