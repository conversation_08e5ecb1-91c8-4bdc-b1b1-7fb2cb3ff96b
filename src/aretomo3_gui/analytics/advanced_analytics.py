#!/usr/bin/env python3
"""
Advanced Analytics System for AreTomo3 GUI
Comprehensive data analysis, quality assessment, and reporting.
"""

import os
import logging
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from datetime import datetime
import json

logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    logger.warning("Matplotlib/Seaborn not available - plotting disabled")

try:
    from scipy import stats, signal
    from scipy.optimize import curve_fit
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    logger.warning("SciPy not available - advanced statistics disabled")

try:
    from sklearn.decomposition import PCA
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("scikit-learn not available - ML analytics disabled")


@dataclass
class AnalyticsResult:
    """Result from an analytics operation."""
    analysis_type: str
    dataset_id: str
    timestamp: datetime
    metrics: Dict[str, float]
    plots: List[str]
    summary: str
    recommendations: List[str]
    quality_score: float


@dataclass
class QualityMetrics:
    """Quality assessment metrics."""
    resolution: float
    contrast: float
    drift: float
    defocus_range: Tuple[float, float]
    astigmatism: float
    ice_thickness: float
    overall_score: float


class DataQualityAnalyzer:
    """Analyze data quality metrics."""
    
    def __init__(self):
        self.quality_thresholds = {
            "resolution_excellent": 3.0,  # Angstroms
            "resolution_good": 4.0,
            "contrast_excellent": 0.8,
            "contrast_good": 0.6,
            "drift_excellent": 5.0,  # pixels
            "drift_good": 10.0,
            "astigmatism_excellent": 100.0,  # Angstroms
            "astigmatism_good": 200.0
        }
    
    def analyze_motion_correction(self, motion_data: Dict[str, Any]) -> AnalyticsResult:
        """Analyze motion correction results."""
        try:
            # Extract motion data
            frame_shifts = motion_data.get("frame_shifts", [])
            total_drift = motion_data.get("total_drift", 0.0)
            early_drift = motion_data.get("early_drift", 0.0)
            late_drift = motion_data.get("late_drift", 0.0)
            
            # Calculate metrics
            metrics = {
                "total_drift": float(total_drift),
                "early_drift": float(early_drift),
                "late_drift": float(late_drift),
                "drift_stability": float(early_drift / late_drift) if late_drift > 0 else 1.0,
                "max_frame_shift": float(max(frame_shifts)) if frame_shifts else 0.0,
                "mean_frame_shift": float(np.mean(frame_shifts)) if frame_shifts else 0.0
            }
            
            # Quality assessment
            quality_score = self._assess_motion_quality(metrics)
            
            # Generate recommendations
            recommendations = self._generate_motion_recommendations(metrics)
            
            # Create summary
            summary = f"Motion correction analysis: Total drift {total_drift:.1f} pixels, Quality score {quality_score:.2f}"
            
            return AnalyticsResult(
                analysis_type="motion_correction",
                dataset_id=motion_data.get("dataset_id", "unknown"),
                timestamp=datetime.now(),
                metrics=metrics,
                plots=[],
                summary=summary,
                recommendations=recommendations,
                quality_score=quality_score
            )
            
        except Exception as e:
            logger.error(f"Motion correction analysis failed: {e}")
            return self._create_error_result("motion_correction", str(e))
    
    def analyze_ctf_estimation(self, ctf_data: Dict[str, Any]) -> AnalyticsResult:
        """Analyze CTF estimation results."""
        try:
            # Extract CTF data
            defocus_u = ctf_data.get("defocus_u", 0.0)
            defocus_v = ctf_data.get("defocus_v", 0.0)
            astigmatism = ctf_data.get("astigmatism", 0.0)
            resolution = ctf_data.get("resolution", 0.0)
            confidence = ctf_data.get("confidence", 0.0)
            
            # Calculate metrics
            mean_defocus = (defocus_u + defocus_v) / 2
            defocus_difference = abs(defocus_u - defocus_v)
            
            metrics = {
                "mean_defocus": float(mean_defocus),
                "defocus_u": float(defocus_u),
                "defocus_v": float(defocus_v),
                "astigmatism": float(astigmatism),
                "defocus_difference": float(defocus_difference),
                "resolution": float(resolution),
                "confidence": float(confidence)
            }
            
            # Quality assessment
            quality_score = self._assess_ctf_quality(metrics)
            
            # Generate recommendations
            recommendations = self._generate_ctf_recommendations(metrics)
            
            # Create summary
            summary = f"CTF estimation: Defocus {mean_defocus:.1f} Å, Resolution {resolution:.1f} Å, Quality {quality_score:.2f}"
            
            return AnalyticsResult(
                analysis_type="ctf_estimation",
                dataset_id=ctf_data.get("dataset_id", "unknown"),
                timestamp=datetime.now(),
                metrics=metrics,
                plots=[],
                summary=summary,
                recommendations=recommendations,
                quality_score=quality_score
            )
            
        except Exception as e:
            logger.error(f"CTF analysis failed: {e}")
            return self._create_error_result("ctf_estimation", str(e))
    
    def _assess_motion_quality(self, metrics: Dict[str, float]) -> float:
        """Assess motion correction quality."""
        total_drift = metrics.get("total_drift", 0.0)
        
        if total_drift <= self.quality_thresholds["drift_excellent"]:
            return 1.0
        elif total_drift <= self.quality_thresholds["drift_good"]:
            return 0.8
        elif total_drift <= 20.0:
            return 0.6
        else:
            return 0.4
    
    def _assess_ctf_quality(self, metrics: Dict[str, float]) -> float:
        """Assess CTF estimation quality."""
        resolution = metrics.get("resolution", 0.0)
        confidence = metrics.get("confidence", 0.0)
        astigmatism = metrics.get("astigmatism", 0.0)
        
        # Resolution score
        if resolution <= self.quality_thresholds["resolution_excellent"]:
            res_score = 1.0
        elif resolution <= self.quality_thresholds["resolution_good"]:
            res_score = 0.8
        else:
            res_score = 0.6
        
        # Confidence score
        conf_score = min(confidence, 1.0)
        
        # Astigmatism score
        if astigmatism <= self.quality_thresholds["astigmatism_excellent"]:
            astig_score = 1.0
        elif astigmatism <= self.quality_thresholds["astigmatism_good"]:
            astig_score = 0.8
        else:
            astig_score = 0.6
        
        # Combined score
        return (res_score + conf_score + astig_score) / 3.0
    
    def _generate_motion_recommendations(self, metrics: Dict[str, float]) -> List[str]:
        """Generate recommendations for motion correction."""
        recommendations = []
        
        total_drift = metrics.get("total_drift", 0.0)
        early_drift = metrics.get("early_drift", 0.0)
        
        if total_drift > self.quality_thresholds["drift_good"]:
            recommendations.append("High drift detected - check sample preparation and grid quality")
        
        if early_drift > total_drift * 0.7:
            recommendations.append("High early drift - consider longer equilibration time")
        
        if metrics.get("drift_stability", 1.0) > 2.0:
            recommendations.append("Unstable drift pattern - check mechanical stability")
        
        if not recommendations:
            recommendations.append("Motion correction quality is good")
        
        return recommendations
    
    def _generate_ctf_recommendations(self, metrics: Dict[str, float]) -> List[str]:
        """Generate recommendations for CTF estimation."""
        recommendations = []
        
        resolution = metrics.get("resolution", 0.0)
        astigmatism = metrics.get("astigmatism", 0.0)
        confidence = metrics.get("confidence", 0.0)
        
        if resolution > self.quality_thresholds["resolution_good"]:
            recommendations.append("Low resolution - check ice thickness and defocus range")
        
        if astigmatism > self.quality_thresholds["astigmatism_good"]:
            recommendations.append("High astigmatism - check objective lens alignment")
        
        if confidence < 0.7:
            recommendations.append("Low CTF confidence - consider different defocus range")
        
        if not recommendations:
            recommendations.append("CTF estimation quality is good")
        
        return recommendations
    
    def _create_error_result(self, analysis_type: str, error_msg: str) -> AnalyticsResult:
        """Create error result."""
        return AnalyticsResult(
            analysis_type=analysis_type,
            dataset_id="error",
            timestamp=datetime.now(),
            metrics={},
            plots=[],
            summary=f"Analysis failed: {error_msg}",
            recommendations=["Check input data and try again"],
            quality_score=0.0
        )


class StatisticalAnalyzer:
    """Perform statistical analysis on datasets."""
    
    def __init__(self):
        self.analysis_cache = {}
    
    def analyze_dataset_trends(self, datasets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze trends across multiple datasets."""
        if not SCIPY_AVAILABLE or not datasets:
            return {"error": "SciPy not available or no data"}
        
        try:
            # Extract metrics
            df = pd.DataFrame(datasets)
            
            # Time series analysis
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.sort_values('timestamp')
            
            # Statistical summary
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            summary_stats = df[numeric_cols].describe()
            
            # Correlation analysis
            correlation_matrix = df[numeric_cols].corr()
            
            # Trend analysis
            trends = {}
            for col in numeric_cols:
                if len(df) > 3:
                    x = np.arange(len(df))
                    y = df[col].values
                    slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)
                    trends[col] = {
                        "slope": slope,
                        "r_squared": r_value**2,
                        "p_value": p_value,
                        "trend": "increasing" if slope > 0 else "decreasing"
                    }
            
            return {
                "summary_statistics": summary_stats.to_dict(),
                "correlations": correlation_matrix.to_dict(),
                "trends": trends,
                "dataset_count": len(datasets)
            }
            
        except Exception as e:
            logger.error(f"Dataset trend analysis failed: {e}")
            return {"error": str(e)}
    
    def perform_outlier_detection(self, data: List[float], method: str = "iqr") -> Dict[str, Any]:
        """Detect outliers in data."""
        if not data:
            return {"outliers": [], "outlier_indices": []}
        
        data_array = np.array(data)
        outlier_indices = []
        
        if method == "iqr":
            Q1 = np.percentile(data_array, 25)
            Q3 = np.percentile(data_array, 75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outlier_indices = np.where((data_array < lower_bound) | (data_array > upper_bound))[0]
        
        elif method == "zscore" and SCIPY_AVAILABLE:
            z_scores = np.abs(stats.zscore(data_array))
            outlier_indices = np.where(z_scores > 3)[0]
        
        outliers = data_array[outlier_indices].tolist()
        
        return {
            "outliers": outliers,
            "outlier_indices": outlier_indices.tolist(),
            "outlier_count": len(outliers),
            "outlier_percentage": len(outliers) / len(data) * 100
        }


class MachineLearningAnalyzer:
    """Machine learning-based analysis."""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
    
    def perform_clustering_analysis(self, data: List[Dict[str, Any]], 
                                  features: List[str], n_clusters: int = 3) -> Dict[str, Any]:
        """Perform clustering analysis on datasets."""
        if not SKLEARN_AVAILABLE:
            return {"error": "scikit-learn not available"}
        
        try:
            # Prepare data
            df = pd.DataFrame(data)
            feature_data = df[features].dropna()
            
            if len(feature_data) < n_clusters:
                return {"error": "Not enough data points for clustering"}
            
            # Scale features
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(feature_data)
            
            # Perform clustering
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(scaled_data)
            
            # Analyze clusters
            cluster_analysis = {}
            for i in range(n_clusters):
                cluster_mask = cluster_labels == i
                cluster_data = feature_data[cluster_mask]
                
                cluster_analysis[f"cluster_{i}"] = {
                    "size": int(np.sum(cluster_mask)),
                    "percentage": float(np.sum(cluster_mask) / len(cluster_labels) * 100),
                    "mean_values": cluster_data.mean().to_dict(),
                    "std_values": cluster_data.std().to_dict()
                }
            
            return {
                "cluster_labels": cluster_labels.tolist(),
                "cluster_centers": kmeans.cluster_centers_.tolist(),
                "cluster_analysis": cluster_analysis,
                "inertia": float(kmeans.inertia_)
            }
            
        except Exception as e:
            logger.error(f"Clustering analysis failed: {e}")
            return {"error": str(e)}
    
    def perform_pca_analysis(self, data: List[Dict[str, Any]], 
                           features: List[str]) -> Dict[str, Any]:
        """Perform Principal Component Analysis."""
        if not SKLEARN_AVAILABLE:
            return {"error": "scikit-learn not available"}
        
        try:
            # Prepare data
            df = pd.DataFrame(data)
            feature_data = df[features].dropna()
            
            if len(feature_data) < 2:
                return {"error": "Not enough data for PCA"}
            
            # Scale features
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(feature_data)
            
            # Perform PCA
            pca = PCA()
            pca_result = pca.fit_transform(scaled_data)
            
            # Calculate explained variance
            explained_variance_ratio = pca.explained_variance_ratio_
            cumulative_variance = np.cumsum(explained_variance_ratio)
            
            return {
                "explained_variance_ratio": explained_variance_ratio.tolist(),
                "cumulative_variance": cumulative_variance.tolist(),
                "components": pca.components_.tolist(),
                "transformed_data": pca_result.tolist(),
                "feature_importance": {
                    feature: abs(pca.components_[0][i]) 
                    for i, feature in enumerate(features)
                }
            }
            
        except Exception as e:
            logger.error(f"PCA analysis failed: {e}")
            return {"error": str(e)}


class AdvancedAnalytics:
    """Main advanced analytics coordinator."""
    
    def __init__(self):
        self.quality_analyzer = DataQualityAnalyzer()
        self.statistical_analyzer = StatisticalAnalyzer()
        self.ml_analyzer = MachineLearningAnalyzer()
        
        # Analysis history
        self.analysis_history = []
        
        logger.info("Advanced analytics system initialized")
    
    def analyze_processing_results(self, results: Dict[str, Any]) -> AnalyticsResult:
        """Analyze processing results comprehensively."""
        analysis_type = results.get("type", "unknown")
        
        if analysis_type == "motion_correction":
            return self.quality_analyzer.analyze_motion_correction(results)
        elif analysis_type == "ctf_estimation":
            return self.quality_analyzer.analyze_ctf_estimation(results)
        else:
            logger.warning(f"Unknown analysis type: {analysis_type}")
            return self.quality_analyzer._create_error_result(analysis_type, "Unknown analysis type")
    
    def generate_comprehensive_report(self, dataset_id: str, 
                                    all_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive analysis report."""
        try:
            # Analyze individual results
            individual_analyses = []
            for result in all_results:
                analysis = self.analyze_processing_results(result)
                individual_analyses.append(analysis)
            
            # Overall quality assessment
            quality_scores = [a.quality_score for a in individual_analyses]
            overall_quality = np.mean(quality_scores) if quality_scores else 0.0
            
            # Statistical analysis
            dataset_trends = self.statistical_analyzer.analyze_dataset_trends(all_results)
            
            # Generate summary
            summary = {
                "dataset_id": dataset_id,
                "timestamp": datetime.now().isoformat(),
                "overall_quality_score": overall_quality,
                "total_analyses": len(individual_analyses),
                "individual_analyses": [
                    {
                        "type": a.analysis_type,
                        "quality_score": a.quality_score,
                        "summary": a.summary,
                        "recommendations": a.recommendations
                    }
                    for a in individual_analyses
                ],
                "statistical_analysis": dataset_trends,
                "recommendations": self._generate_overall_recommendations(individual_analyses)
            }
            
            # Store in history
            self.analysis_history.append(summary)
            
            return summary
            
        except Exception as e:
            logger.error(f"Comprehensive report generation failed: {e}")
            return {"error": str(e)}
    
    def _generate_overall_recommendations(self, analyses: List[AnalyticsResult]) -> List[str]:
        """Generate overall recommendations from multiple analyses."""
        all_recommendations = []
        for analysis in analyses:
            all_recommendations.extend(analysis.recommendations)
        
        # Remove duplicates and prioritize
        unique_recommendations = list(set(all_recommendations))
        
        # Add overall recommendations
        quality_scores = [a.quality_score for a in analyses]
        avg_quality = np.mean(quality_scores) if quality_scores else 0.0
        
        if avg_quality < 0.6:
            unique_recommendations.insert(0, "Overall data quality is below optimal - review acquisition parameters")
        elif avg_quality > 0.8:
            unique_recommendations.insert(0, "Excellent data quality - proceed with downstream processing")
        
        return unique_recommendations[:10]  # Limit to top 10 recommendations
    
    def get_analytics_summary(self) -> Dict[str, Any]:
        """Get summary of analytics system."""
        return {
            "total_analyses": len(self.analysis_history),
            "available_analyzers": {
                "quality_analyzer": True,
                "statistical_analyzer": SCIPY_AVAILABLE,
                "ml_analyzer": SKLEARN_AVAILABLE,
                "plotting": PLOTTING_AVAILABLE
            },
            "recent_analyses": self.analysis_history[-5:] if self.analysis_history else []
        }
