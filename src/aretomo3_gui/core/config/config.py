# Default configuration settings
import os
from pathlib import Path

# Project paths
PROJECT_ROOT = Path(os.path.dirname(os.path.abspath(__file__)))
CACHE_DIR = PROJECT_ROOT / ".cache"
LOG_DIR = PROJECT_ROOT / "logs"

# Create necessary directories
CACHE_DIR.mkdir(exist_ok=True)
LOG_DIR.mkdir(exist_ok=True)

# Microscope settings
MICROSCOPE_SETTINGS = {
    "voltage": 300,  # kV
    "cs": 2.7e7,  # Spherical aberration in Angstroms
    "pixel_size": 1.0,  # Angstroms per pixel
}

# GUI preferences
GUI_SETTINGS = {
    "dark_mode": False,
    "auto_save": True,
    "window_state": None,
    "window_geometry": None,
}

# Analysis settings
ANALYSIS_SETTINGS = {
    "plot_dpi": 300,
    "plot_format": "png",
    "max_recent_files": 10,
}

# AreTomo3 settings
ARETOMO3_SETTINGS = {
    "binary_path": os.environ.get("ARETOMO3_PATH", ""),
    "default_gpu": 0,
    "max_parallel_jobs": 1,
}

# Logging configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_name": LOG_DIR / "aretomo3_gui.log",
}
