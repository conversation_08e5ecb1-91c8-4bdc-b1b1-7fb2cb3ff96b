"""
Enhanced Real-time Processor with Live Monitoring
Integrates with AreTomo3 GUI for real-time processing capabilities.
"""

import logging
import os
import threading
import time
from pathlib import Path

from PyQt6.QtCore import QObject, pyqtSignal
from watchdog.events import FileSystemEventHandler
from watchdog.observers import Observer

logger = logging.getLogger(__name__)


class EnhancedRealTimeProcessor(QObject):
    """Enhanced real-time processor with Qt signals for GUI integration."""

    # Qt signals for GUI updates
    file_detected = pyqtSignal(str)
    processing_started = pyqtSignal(str)
    processing_completed = pyqtSignal(str, bool)
    stats_updated = pyqtSignal(dict)

    def __init__(self, watch_dirs, output_dir, parent=None):
        super().__init__(parent)
        self.watch_directories = [Path(d) for d in watch_dirs]
        self.output_directory = Path(output_dir)
        self.observer = Observer()
        self.is_running = False
        self.processing_queue = []
        self.processing_thread = None
        self.auto_processing_enabled = True

        # Statistics
        self.stats = {
            "files_detected": 0,
            "files_processed": 0,
            "files_failed": 0,
            "processing_time_total": 0.0,
            "queue_size": 0,
            "throughput_per_hour": 0.0,
        }

        # Setup event handler
        self.event_handler = EnhancedTiltSeriesEventHandler(self)

    def start_monitoring(self):
        """Start real-time monitoring."""
        if self.is_running:
            return

        logger.info("Starting enhanced real-time monitoring...")

        # Ensure output directory exists
        self.output_directory.mkdir(parents=True, exist_ok=True)

        # Start monitoring watch directories
        for watch_dir in self.watch_directories:
            if watch_dir.exists():
                self.observer.schedule(
                    self.event_handler, str(watch_dir), recursive=True
                )
                logger.info(f"Monitoring: {watch_dir}")

        self.observer.start()
        self.is_running = True

        # Start processing worker thread
        self.processing_thread = threading.Thread(
            target=self._processing_worker, daemon=True
        )
        self.processing_thread.start()

        logger.info("✅ Enhanced real-time monitoring started")

    def stop_monitoring(self):
        """Stop real-time monitoring."""
        if not self.is_running:
            return

        logger.info("Stopping enhanced real-time monitoring...")

        self.is_running = False
        self.observer.stop()
        self.observer.join()

        if self.processing_thread:
            self.processing_thread.join(timeout=5)

        logger.info("✅ Enhanced real-time monitoring stopped")

    def add_file_to_queue(self, file_path):
        """Add file to processing queue and emit signal."""
        file_path = Path(file_path)
        if file_path not in self.processing_queue:
            self.processing_queue.append(file_path)
            self.stats["files_detected"] += 1
            self.stats["queue_size"] = len(self.processing_queue)

            # Emit signals for GUI updates
            self.file_detected.emit(str(file_path))
            self.stats_updated.emit(self.stats.copy())

            logger.info(f"File added to queue: {file_path}")

    def _processing_worker(self):
        """Background processing worker."""
        while self.is_running:
            if self.processing_queue and self.auto_processing_enabled:
                file_path = self.processing_queue.pop(0)
                self.stats["queue_size"] = len(self.processing_queue)
                self._process_file_with_signals(file_path)
            else:
                time.sleep(1)

    def _process_file_with_signals(self, file_path):
        """Process file and emit appropriate signals."""
        start_time = time.time()

        # Emit processing started signal
        self.processing_started.emit(str(file_path))

        try:
            # Simulate AreTomo3 processing
            success = self._simulate_aretomo_processing(file_path)

            processing_time = time.time() - start_time
            self.stats["processing_time_total"] += processing_time

            if success:
                self.stats["files_processed"] += 1
            else:
                self.stats["files_failed"] += 1

            # Calculate throughput
            if self.stats["files_processed"] > 0:
                avg_time = (
                    self.stats["processing_time_total"] / self.stats["files_processed"]
                )
                self.stats["throughput_per_hour"] = (
                    3600 / avg_time if avg_time > 0 else 0
                )

            # Emit completion signal
            self.processing_completed.emit(str(file_path), success)
            self.stats_updated.emit(self.stats.copy())

        except Exception as e:
            self.stats["files_failed"] += 1
            self.processing_completed.emit(str(file_path), False)
            self.stats_updated.emit(self.stats.copy())
            logger.error(f"Processing error: {e}")

    def _simulate_aretomo_processing(self, file_path):
        """Simulate AreTomo3 processing (placeholder)."""
        # In real implementation, this would call AreTomo3
        time.sleep(2)  # Simulate processing time

        # Check file validity
        if file_path.suffix.lower() in [".mrc", ".st", ".tif", ".tiff", ".eer"]:
            return True

        return False

    def get_current_stats(self):
        """Get current processing statistics."""
        return self.stats.copy()

    def set_auto_processing(self, enabled):
        """Enable/disable automatic processing."""
        self.auto_processing_enabled = enabled
        logger.info(f"Auto-processing {'enabled' if enabled else 'disabled'}")


class EnhancedTiltSeriesEventHandler(FileSystemEventHandler):
    """Enhanced file system event handler."""

    def __init__(self, processor):
        self.processor = processor
        self.supported_extensions = {".mrc", ".st", ".tif", ".tiff", ".eer"}
        self.file_size_cache = {}

    def on_created(self, event):
        """Handle file creation with stability check."""
        if not event.is_directory:
            file_path = Path(event.src_path)

            if file_path.suffix.lower() in self.supported_extensions:
                # Wait for file to be completely written
                self._wait_for_file_stability(file_path)

    def _wait_for_file_stability(self, file_path):
        """Wait for file to be completely written."""

        def check_stability():
            try:
                # Check file size stability
                current_size = file_path.stat().st_size
                time.sleep(2)

                if file_path.exists():
                    new_size = file_path.stat().st_size
                    if current_size == new_size and new_size > 0:
                        # File is stable, add to queue
                        self.processor.add_file_to_queue(file_path)
                    else:
                        # File still being written, check again
                        threading.Timer(2, check_stability).start()
            except Exception as e:
                logger.error(f"Error checking file stability: {e}")

        # Start stability check in separate thread
        threading.Timer(1, check_stability).start()
