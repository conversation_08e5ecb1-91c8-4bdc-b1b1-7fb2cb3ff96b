#!/usr/bin/env python3
"""
Enhanced Progress Visualization Widget
Real-time 3D progress indicators with processing time predictions and resource utilization graphs.
"""

import time
from collections import deque
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import numpy as np
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QColor, QFont, QPalette
from PyQt6.QtWidgets import (
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QProgressBar,
    QPushButton,
    QSizePolicy,
    QVBoxLayout,
    QWidget,
)

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure

    plt.style.use("default")  # Use a safe default style
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False


class ProcessingTimePredictor:
    """ML-inspired processing time prediction engine."""

    def __init__(self):
        """Initialize the instance."""
        self.processing_history: List[Dict[str, Any]] = []
        self.current_session_start: Optional[datetime] = None
        self.total_files: int = 0
        self.completed_files: int = 0

    def start_session(self, total_files: int):
        """Start a new processing session."""
        self.current_session_start = datetime.now()
        self.total_files = total_files
        self.completed_files = 0

    def update_progress(self, completed_files: int):
        """Update progress and recalculate predictions."""
        self.completed_files = completed_files

    def predict_remaining_time(self) -> Optional[timedelta]:
        """Predict remaining processing time using multiple methods."""
        if not self.current_session_start or self.completed_files == 0:
            return None

        elapsed = datetime.now() - self.current_session_start

        # Method 1: Linear extrapolation from current session
        files_per_second = self.completed_files / elapsed.total_seconds()
        remaining_files = self.total_files - self.completed_files

        if files_per_second > 0:
            estimated_seconds = remaining_files / files_per_second
            return timedelta(seconds=estimated_seconds)

        return None

    def get_efficiency_score(self) -> float:
        """Calculate processing efficiency score (0-100)."""
        if not self.current_session_start or self.completed_files == 0:
            return 0.0

        elapsed_minutes = (
            datetime.now() - self.current_session_start
        ).total_seconds() / 60
        files_per_minute = (
            self.completed_files / elapsed_minutes if elapsed_minutes > 0 else 0
        )

        # Base efficiency on files per minute (adjust scale as needed)
        efficiency = min(100.0, files_per_minute * 20)  # Arbitrary scaling
        return efficiency


class ResourceUtilizationGraph(QWidget):
    """Real-time resource utilization graph widget."""

    def __init__(self, title: str, max_points: int = 60):
        """Initialize the instance."""
        super().__init__()
        self.title = title
        self.max_points = max_points
        self.data_points = deque(maxlen=max_points)
        self.timestamps = deque(maxlen=max_points)

        self.setup_ui()

    def setup_ui(self):
        """Set up the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Title
        title_label = QLabel(self.title)
        title_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        if MATPLOTLIB_AVAILABLE:
            # Matplotlib graph
            self.figure = Figure(figsize=(4, 2), dpi=80)
            self.canvas = FigureCanvas(self.figure)
            self.axes = self.figure.add_subplot(111)

            # Style the plot
            self.figure.patch.set_facecolor("white")
            self.axes.set_facecolor("#f8f9fa")
            self.axes.grid(True, alpha=0.3)
            self.axes.set_ylim(0, 100)

            layout.addWidget(self.canvas)
        else:
            # Fallback to simple progress bar
            self.progress_bar = QProgressBar()
            self.progress_bar.setRange(0, 100)
            layout.addWidget(self.progress_bar)

        # Current value label
        self.value_label = QLabel("0%")
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.value_label.setFont(QFont("Arial", 9))
        layout.addWidget(self.value_label)

    def add_data_point(self, value: float):
        """Add a new data point and update the graph."""
        self.data_points.append(max(0, min(100, value)))  # Clamp to 0-100
        self.timestamps.append(datetime.now())

        self.value_label.setText(f"{value:.1f}%")

        if MATPLOTLIB_AVAILABLE:
            self.update_graph()
        else:
            self.progress_bar.setValue(int(value))

    def update_graph(self):
        """Update the matplotlib graph."""
        if not MATPLOTLIB_AVAILABLE or len(self.data_points) == 0:
            return

        self.axes.clear()

        # Plot data
        if len(self.data_points) > 1:
            self.axes.plot(
                range(len(self.data_points)),
                list(self.data_points),
                color="#2196F3",
                linewidth=2,
                alpha=0.8,
            )
            self.axes.fill_between(
                range(len(self.data_points)),
                list(self.data_points),
                alpha=0.3,
                color="#2196F3",
            )

        # Style
        self.axes.set_ylim(0, 100)
        self.axes.set_xlim(0, max(self.max_points, len(self.data_points)))
        self.axes.grid(True, alpha=0.3)
        self.axes.set_ylabel("Usage %", fontsize=8)

        # Remove x-axis labels for cleaner look
        self.axes.set_xticks([])

        self.figure.tight_layout()
        self.canvas.draw()


class Enhanced3DProgressIndicator(QWidget):
    """3D-style progress indicator with animation effects."""

    def __init__(self):
        """Initialize the instance."""
        super().__init__()
        self.current_value = 0
        self.target_value = 0
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.animate_progress)

        self.setup_ui()

    def setup_ui(self):
        """Set up the 3D progress indicator."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # Main progress bar with enhanced styling
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setMinimumHeight(30)

        # Enhanced 3D styling
        self.progress_bar.setStyleSheet(
            """
            QProgressBar {
                border: 2px solid #3498db;
                border-radius: 15px;
                text-align: center;
                font-weight: bold;
                font-size: 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ecf0f1, stop:1 #bdc3c7);
            }
            QProgressBar::chunk {
                border-radius: 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:0.5 #2980b9, stop:1 #1abc9c);
            }
        """
        )

        layout.addWidget(self.progress_bar)

        # Progress details
        details_layout = QHBoxLayout()

        self.stage_label = QLabel("Ready")
        self.stage_label.setFont(QFont("Arial", 9))
        details_layout.addWidget(self.stage_label)

        details_layout.addStretch()

        self.eta_label = QLabel("ETA: --")
        self.eta_label.setFont(QFont("Arial", 9))
        details_layout.addWidget(self.eta_label)

        layout.addLayout(details_layout)

    def set_progress(self, value: int, stage: str = "", eta: str = ""):
        """Set progress value with smooth animation."""
        self.target_value = max(0, min(100, value))

        if stage:
            self.stage_label.setText(stage)
        if eta:
            self.eta_label.setText(f"ETA: {eta}")

        # Start animation if not already running
        if not self.animation_timer.isActive():
            self.animation_timer.start(50)  # 20 FPS animation

    def animate_progress(self):
        """Animate progress bar to target value."""
        if abs(self.current_value - self.target_value) < 0.5:
            self.current_value = self.target_value
            self.animation_timer.stop()
        else:
            # Smooth interpolation
            diff = self.target_value - self.current_value
            self.current_value += diff * 0.1

        self.progress_bar.setValue(int(self.current_value))


class EnhancedProgressVisualization(QWidget):
    """
    Enhanced Progress Visualization Widget

    Features:
    - Real-time 3D progress indicators
    - Processing time predictions
    - Resource utilization graphs
    - Efficiency metrics
    """

    # Signals
    processing_started = pyqtSignal(int)  # total_files
    processing_updated = pyqtSignal(int)  # completed_files
    processing_finished = pyqtSignal()

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)

        # Initialize components
        self.time_predictor = ProcessingTimePredictor()
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_displays)

        self.setup_ui()

    def setup_ui(self):
        """Set up the enhanced progress visualization UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # Main progress section
        progress_group = QGroupBox("🚀 Processing Progress")
        progress_group.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        progress_layout = QVBoxLayout(progress_group)

        # 3D Progress indicator
        self.progress_indicator = Enhanced3DProgressIndicator()
        progress_layout.addWidget(self.progress_indicator)

        # Time prediction section
        time_layout = QHBoxLayout()

        self.elapsed_label = QLabel("Elapsed: 00:00:00")
        self.elapsed_label.setFont(QFont("Arial", 9))
        time_layout.addWidget(self.elapsed_label)

        time_layout.addStretch()

        self.remaining_label = QLabel("Remaining: --:--:--")
        self.remaining_label.setFont(QFont("Arial", 9))
        time_layout.addWidget(self.remaining_label)

        time_layout.addStretch()

        self.efficiency_label = QLabel("Efficiency: --%")
        self.efficiency_label.setFont(QFont("Arial", 9))
        time_layout.addWidget(self.efficiency_label)

        progress_layout.addLayout(time_layout)
        layout.addWidget(progress_group)

        # Resource utilization section
        resource_group = QGroupBox("📊 System Resources")
        resource_group.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        resource_layout = QGridLayout(resource_group)

        # Create resource graphs
        self.cpu_graph = ResourceUtilizationGraph("CPU Usage")
        self.memory_graph = ResourceUtilizationGraph("Memory Usage")
        self.gpu_graph = ResourceUtilizationGraph("GPU Usage")

        resource_layout.addWidget(self.cpu_graph, 0, 0)
        resource_layout.addWidget(self.memory_graph, 0, 1)
        resource_layout.addWidget(self.gpu_graph, 0, 2)

        layout.addWidget(resource_group)

        # Control buttons
        controls_layout = QHBoxLayout()
        controls_layout.addStretch()

        self.pause_btn = QPushButton("⏸️ Pause")
        self.pause_btn.clicked.connect(self.toggle_pause)
        self.pause_btn.setEnabled(False)
        controls_layout.addWidget(self.pause_btn)

        self.stop_btn = QPushButton("⏹️ Stop")
        self.stop_btn.setEnabled(False)
        controls_layout.addWidget(self.stop_btn)

        layout.addLayout(controls_layout)

        # Start update timer
        self.update_timer.start(1000)  # Update every second

    def start_processing(self, total_files: int):
        """Start a new processing session."""
        self.time_predictor.start_session(total_files)
        self.progress_indicator.set_progress(0, "Starting...", "Calculating...")

        self.pause_btn.setEnabled(True)
        self.stop_btn.setEnabled(True)

        self.processing_started.emit(total_files)

    def update_progress(self, completed_files: int, current_stage: str = ""):
        """Update processing progress."""
        self.time_predictor.update_progress(completed_files)

        if self.time_predictor.total_files > 0:
            progress_percent = (completed_files / self.time_predictor.total_files) * 100

            # Update time prediction
            remaining = self.time_predictor.predict_remaining_time()
            eta_str = (
                self.format_timedelta(remaining) if remaining else "Calculating..."
            )

            stage_str = (
                current_stage
                or f"Processing {completed_files}/{self.time_predictor.total_files}"
            )

            self.progress_indicator.set_progress(
                int(progress_percent), stage_str, eta_str
            )

        self.processing_updated.emit(completed_files)

    def finish_processing(self):
        """Finish processing session."""
        self.progress_indicator.set_progress(100, "Complete!", "✅ Finished")

        self.pause_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)

        self.processing_finished.emit()

    def update_resource_usage(
        self, cpu_percent: float, memory_percent: float, gpu_percent: float = 0.0
    ):
        """Update resource utilization graphs."""
        self.cpu_graph.add_data_point(cpu_percent)
        self.memory_graph.add_data_point(memory_percent)
        self.gpu_graph.add_data_point(gpu_percent)

    def update_displays(self):
        """Update time displays and efficiency metrics."""
        if self.time_predictor.current_session_start:
            elapsed = datetime.now() - self.time_predictor.current_session_start
            self.elapsed_label.setText(f"Elapsed: {self.format_timedelta(elapsed)}")

            remaining = self.time_predictor.predict_remaining_time()
            if remaining:
                self.remaining_label.setText(
                    f"Remaining: {self.format_timedelta(remaining)}"
                )

            efficiency = self.time_predictor.get_efficiency_score()
            self.efficiency_label.setText(f"Efficiency: {efficiency:.1f}%")

    def toggle_pause(self):
        """Toggle pause state."""
        if self.pause_btn.text() == "⏸️ Pause":
            self.pause_btn.setText("▶️ Resume")
            self.progress_indicator.set_progress(
                self.progress_indicator.current_value, "⏸️ Paused", "Paused"
            )
        else:
            self.pause_btn.setText("⏸️ Pause")

    @staticmethod
    def format_timedelta(td: timedelta) -> str:
        """Format timedelta as HH:MM:SS."""
        if not td:
            return "--:--:--"

        total_seconds = int(td.total_seconds())
        hours, remainder = divmod(total_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)

        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"


# Example usage and testing
if __name__ == "__main__":
    import sys

    from PyQt6.QtWidgets import QApplication, QMainWindow

    class TestWindow(QMainWindow):
        """Class TestWindow implementation."""

        def __init__(self):
            """Initialize the instance."""
            super().__init__()
            self.setWindowTitle("Enhanced Progress Visualization Test")
            self.setGeometry(100, 100, 800, 600)

            # Create and set central widget
            self.progress_widget = EnhancedProgressVisualization()
            self.setCentralWidget(self.progress_widget)

            # Test data simulation
            self.test_timer = QTimer()
            self.test_timer.timeout.connect(self.simulate_progress)
            self.test_files = 0
            self.total_test_files = 20

            # Start test
            self.start_test()

        def start_test(self):
            """Start progress test simulation."""
            self.progress_widget.start_processing(self.total_test_files)
            self.test_timer.start(500)  # Update every 500ms for demo

        def simulate_progress(self):
            """Simulate processing progress."""
            self.test_files += 1

            # Simulate resource usage
            cpu = 50 + 30 * np.sin(self.test_files * 0.1) + np.random.random() * 10
            memory = 60 + 20 * np.cos(self.test_files * 0.15) + np.random.random() * 10
            gpu = 70 + 25 * np.sin(self.test_files * 0.2) + np.random.random() * 15

            self.progress_widget.update_resource_usage(cpu, memory, gpu)
            self.progress_widget.update_progress(
                self.test_files, f"Processing file {self.test_files}"
            )

            if self.test_files >= self.total_test_files:
                self.test_timer.stop()
                self.progress_widget.finish_processing()

    # Run test application
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec())
