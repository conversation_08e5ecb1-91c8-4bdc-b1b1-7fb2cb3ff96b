"""
Embedded Motion Correction Viewer Widget
Fully integrated motion correction visualization directly in the main GUI
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from matplotlib.patches import Circle, Arrow
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSlider, QPushButton,
    QCheckBox, QSpinBox, QDoubleSpinBox, QGroupBox, QGridLayout,
    QComboBox, QProgressBar, QTextEdit, QSplitter, QFrame, QTableWidget,
    QTableWidgetItem, QHeaderView
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QPalette
import logging
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
from scipy import ndimage

logger = logging.getLogger(__name__)


class EmbeddedMotionViewer(QWidget):
    """Fully embedded motion correction viewer widget for the main GUI."""

    # Signals for communication with main GUI
    motion_data_loaded = pyqtSignal(dict)
    motion_analysis_updated = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.motion_data = None
        self.current_tilt_idx = 0
        self.motion_images = None
        self.motion_trajectories = None
        self.setup_ui()
        self.setup_matplotlib()

    def setup_ui(self):
        """Setup the embedded motion viewer UI."""
        layout = QVBoxLayout(self)

        # Header with title and controls
        header = self.create_header()
        layout.addWidget(header)

        # Main content area with splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel: Motion visualization
        viz_panel = self.create_visualization_panel()
        splitter.addWidget(viz_panel)

        # Right panel: Controls and statistics
        control_panel = self.create_control_panel()
        splitter.addWidget(control_panel)

        # Set splitter proportions (70% viz, 30% controls)
        splitter.setSizes([700, 300])
        layout.addWidget(splitter)

        # Status bar
        self.status_label = QLabel("Ready - Load motion correction data to begin")
        self.status_label.setStyleSheet("QLabel { color: #666; font-style: italic; }")
        layout.addWidget(self.status_label)

    def create_header(self):
        """Create header with title and main controls."""
        header = QFrame()
        header.setFrameStyle(QFrame.Shape.StyledPanel)
        header.setStyleSheet("QFrame { background-color: #f0f0f0; border-radius: 5px; }")

        layout = QHBoxLayout(header)

        # Title
        title = QLabel("🎯 Motion Correction Viewer")
        title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title)

        layout.addStretch()

        # Quick action buttons
        self.load_btn = QPushButton("📁 Load Motion Data")
        self.load_btn.clicked.connect(self.load_motion_data)
        layout.addWidget(self.load_btn)

        self.analyze_btn = QPushButton("📊 Analyze Motion")
        self.analyze_btn.clicked.connect(self.analyze_motion)
        self.analyze_btn.setEnabled(False)
        layout.addWidget(self.analyze_btn)

        self.export_btn = QPushButton("💾 Export Results")
        self.export_btn.clicked.connect(self.export_results)
        self.export_btn.setEnabled(False)
        layout.addWidget(self.export_btn)

        return header

    def create_visualization_panel(self):
        """Create the main visualization panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Matplotlib figure for motion visualization
        self.figure = Figure(figsize=(12, 8), facecolor='white')
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        # Navigation controls
        nav_layout = QHBoxLayout()

        # Tilt navigation
        nav_layout.addWidget(QLabel("Tilt:"))
        self.tilt_slider = QSlider(Qt.Orientation.Horizontal)
        self.tilt_slider.setMinimum(0)
        self.tilt_slider.setMaximum(0)
        self.tilt_slider.valueChanged.connect(self.on_tilt_changed)
        nav_layout.addWidget(self.tilt_slider)

        self.tilt_label = QLabel("0/0")
        nav_layout.addWidget(self.tilt_label)

        # Previous/Next buttons
        self.prev_btn = QPushButton("◀ Prev")
        self.prev_btn.clicked.connect(self.prev_tilt)
        nav_layout.addWidget(self.prev_btn)

        self.next_btn = QPushButton("Next ▶")
        self.next_btn.clicked.connect(self.next_tilt)
        nav_layout.addWidget(self.next_btn)

        # Play/Pause animation
        self.play_btn = QPushButton("▶ Play")
        self.play_btn.clicked.connect(self.toggle_animation)
        nav_layout.addWidget(self.play_btn)

        layout.addLayout(nav_layout)

        return panel

    def create_control_panel(self):
        """Create the control and analysis panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Display Options Group
        display_group = QGroupBox("Display Options")
        display_layout = QVBoxLayout(display_group)

        self.show_trajectories_cb = QCheckBox("Show Motion Trajectories")
        self.show_trajectories_cb.setChecked(True)
        self.show_trajectories_cb.toggled.connect(self.update_display)
        display_layout.addWidget(self.show_trajectories_cb)

        self.show_vectors_cb = QCheckBox("Show Motion Vectors")
        self.show_vectors_cb.setChecked(False)
        self.show_vectors_cb.toggled.connect(self.update_display)
        display_layout.addWidget(self.show_vectors_cb)

        self.show_grid_cb = QCheckBox("Show Reference Grid")
        self.show_grid_cb.setChecked(False)
        self.show_grid_cb.toggled.connect(self.update_display)
        display_layout.addWidget(self.show_grid_cb)

        # Contrast controls
        contrast_layout = QHBoxLayout()
        contrast_layout.addWidget(QLabel("Contrast:"))
        self.contrast_slider = QSlider(Qt.Orientation.Horizontal)
        self.contrast_slider.setRange(1, 100)
        self.contrast_slider.setValue(50)
        self.contrast_slider.valueChanged.connect(self.update_display)
        contrast_layout.addWidget(self.contrast_slider)
        display_layout.addLayout(contrast_layout)

        layout.addWidget(display_group)

        # Motion Statistics Group
        stats_group = QGroupBox("Motion Statistics")
        stats_layout = QVBoxLayout(stats_group)

        # Statistics table
        self.stats_table = QTableWidget()
        self.stats_table.setColumnCount(2)
        self.stats_table.setHorizontalHeaderLabels(["Parameter", "Value"])
        self.stats_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.stats_table.setMaximumHeight(200)
        stats_layout.addWidget(self.stats_table)

        layout.addWidget(stats_group)

        # Quality Assessment Group
        quality_group = QGroupBox("Quality Assessment")
        quality_layout = QVBoxLayout(quality_group)

        self.quality_text = QTextEdit()
        self.quality_text.setMaximumHeight(120)
        self.quality_text.setReadOnly(True)
        quality_layout.addWidget(self.quality_text)

        layout.addWidget(quality_group)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        layout.addStretch()

        return panel

    def setup_matplotlib(self):
        """Setup matplotlib plots."""
        self.figure.clear()

        # Create subplots: 2x2 layout
        gs = self.figure.add_gridspec(2, 2, height_ratios=[2, 1], width_ratios=[2, 1])

        # Main motion corrected image (top-left)
        self.ax_main = self.figure.add_subplot(gs[0, 0])
        self.ax_main.set_title("Motion Corrected Image")
        self.ax_main.set_xlabel("X (pixels)")
        self.ax_main.set_ylabel("Y (pixels)")

        # Motion trajectory plot (top-right)
        self.ax_trajectory = self.figure.add_subplot(gs[0, 1])
        self.ax_trajectory.set_title("Motion Trajectory")
        self.ax_trajectory.set_xlabel("X Drift (pixels)")
        self.ax_trajectory.set_ylabel("Y Drift (pixels)")

        # Motion vs time (bottom, spanning both columns)
        self.ax_time = self.figure.add_subplot(gs[1, :])
        self.ax_time.set_title("Motion vs Time")
        self.ax_time.set_xlabel("Frame Number")
        self.ax_time.set_ylabel("Drift (pixels)")

        self.figure.tight_layout()
        self.canvas.draw()

    def load_motion_data(self):
        """Load motion correction data from file or directory."""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Motion Data", "",
            "Motion Files (*.xf *.txt *.log);;All Files (*)"
        )

        if file_path:
            self.load_motion_from_file(file_path)

    def load_motion_from_file(self, file_path: str):
        """Load motion correction data from specific file."""
        try:
            self.status_label.setText(f"Loading motion data from {Path(file_path).name}...")

            # Parse motion data (simplified - would use actual parser)
            self.motion_data = self.parse_motion_file(file_path)

            if self.motion_data:
                self.setup_navigation()
                self.update_display()
                self.update_statistics()
                self.analyze_btn.setEnabled(True)
                self.export_btn.setEnabled(False)
                self.status_label.setText(f"Loaded motion data for {len(self.motion_data.get('tilts', []))} tilts")
                self.motion_data_loaded.emit(self.motion_data)
            else:
                self.status_label.setText("Failed to load motion data")

        except Exception as e:
            logger.error(f"Error loading motion data: {e}")
            self.status_label.setText(f"Error: {e}")

    def parse_motion_file(self, file_path: str) -> Dict:
        """Parse motion correction file (simplified implementation)."""
        # This would integrate with your existing motion parser
        # For now, create dummy data for demonstration

        n_tilts = 41  # Typical tilt series
        tilts = np.linspace(-60, 60, n_tilts)

        # Generate dummy motion corrected images and trajectories
        size = 512
        motion_images = []
        motion_trajectories = []
        motion_stats = []

        for i, tilt in enumerate(tilts):
            # Create dummy motion corrected image
            # Simulate some structure with noise
            x, y = np.meshgrid(np.linspace(-1, 1, size), np.linspace(-1, 1, size))
            image = np.exp(-(x**2 + y**2) / 0.3) + 0.1 * np.random.random((size, size))

            # Add some features
            image += 0.5 * np.exp(-((x-0.3)**2 + (y-0.2)**2) / 0.1)
            image += 0.3 * np.exp(-((x+0.2)**2 + (y-0.4)**2) / 0.05)

            motion_images.append(image)

            # Generate dummy motion trajectory (frames within this tilt)
            n_frames = 20 + int(5 * np.random.random())  # Variable frames per tilt

            # Simulate drift - more drift at higher tilts
            base_drift = 2.0 * np.abs(tilt) / 60.0  # More drift at high tilt

            x_drift = np.cumsum(np.random.normal(0, base_drift, n_frames))
            y_drift = np.cumsum(np.random.normal(0, base_drift, n_frames))

            trajectory = {
                'frames': np.arange(n_frames),
                'x_drift': x_drift,
                'y_drift': y_drift,
                'total_drift': np.sqrt(x_drift**2 + y_drift**2)
            }

            motion_trajectories.append(trajectory)

            # Calculate motion statistics
            total_motion = trajectory['total_drift'][-1] if len(trajectory['total_drift']) > 0 else 0
            max_motion = np.max(trajectory['total_drift']) if len(trajectory['total_drift']) > 0 else 0
            mean_motion = np.mean(trajectory['total_drift']) if len(trajectory['total_drift']) > 0 else 0

            motion_stats.append({
                'tilt_angle': tilt,
                'total_motion': total_motion,
                'max_motion': max_motion,
                'mean_motion': mean_motion,
                'n_frames': n_frames
            })

        return {
            'file_path': file_path,
            'tilts': tilts,
            'motion_images': motion_images,
            'motion_trajectories': motion_trajectories,
            'motion_stats': motion_stats,
            'n_tilts': n_tilts
        }

    def setup_navigation(self):
        """Setup navigation controls based on loaded data."""
        if not self.motion_data:
            return

        n_tilts = self.motion_data['n_tilts']
        self.tilt_slider.setMaximum(n_tilts - 1)
        self.tilt_slider.setValue(0)
        self.current_tilt_idx = 0
        self.update_tilt_label()

    def update_tilt_label(self):
        """Update tilt label."""
        if self.motion_data:
            n_tilts = self.motion_data['n_tilts']
            current_tilt = self.motion_data['tilts'][self.current_tilt_idx]
            self.tilt_label.setText(f"{self.current_tilt_idx + 1}/{n_tilts} ({current_tilt:.1f}°)")

    def on_tilt_changed(self, value):
        """Handle tilt slider change."""
        self.current_tilt_idx = value
        self.update_tilt_label()
        self.update_display()
        self.update_statistics()

    def prev_tilt(self):
        """Go to previous tilt."""
        if self.current_tilt_idx > 0:
            self.tilt_slider.setValue(self.current_tilt_idx - 1)

    def next_tilt(self):
        """Go to next tilt."""
        if self.motion_data and self.current_tilt_idx < self.motion_data['n_tilts'] - 1:
            self.tilt_slider.setValue(self.current_tilt_idx + 1)

    def toggle_animation(self):
        """Toggle animation playback."""
        if hasattr(self, 'animation_timer') and self.animation_timer.isActive():
            self.animation_timer.stop()
            self.play_btn.setText("▶ Play")
        else:
            self.animation_timer = QTimer()
            self.animation_timer.timeout.connect(self.animate_next_frame)
            self.animation_timer.start(500)  # 500ms between frames
            self.play_btn.setText("⏸ Pause")

    def animate_next_frame(self):
        """Animate to next frame."""
        if self.motion_data and self.current_tilt_idx < self.motion_data['n_tilts'] - 1:
            self.next_tilt()
        else:
            self.toggle_animation()  # Stop at end

    def update_display(self):
        """Update the motion correction display."""
        if not self.motion_data or self.current_tilt_idx >= len(self.motion_data['motion_images']):
            return

        # Get current data
        motion_image = self.motion_data['motion_images'][self.current_tilt_idx]
        trajectory = self.motion_data['motion_trajectories'][self.current_tilt_idx]

        # Clear axes
        self.ax_main.clear()
        self.ax_trajectory.clear()
        self.ax_time.clear()

        # Display motion corrected image
        contrast = self.contrast_slider.value() / 50.0  # 0.02 to 2.0
        vmin, vmax = np.percentile(motion_image, [1, 99])
        vrange = vmax - vmin
        vmin_adj = vmin + vrange * (1 - contrast) / 2
        vmax_adj = vmax - vrange * (1 - contrast) / 2

        im = self.ax_main.imshow(motion_image, cmap='gray', origin='lower',
                                vmin=vmin_adj, vmax=vmax_adj)
        self.ax_main.set_title(f"Motion Corrected - Tilt {self.motion_data['tilts'][self.current_tilt_idx]:.1f}°")

        # Add reference grid if enabled
        if self.show_grid_cb.isChecked():
            self.add_reference_grid()

        # Plot motion trajectory
        if self.show_trajectories_cb.isChecked():
            self.plot_motion_trajectory(trajectory)

        # Plot motion vectors if enabled
        if self.show_vectors_cb.isChecked():
            self.plot_motion_vectors(trajectory)

        # Plot motion vs time
        self.plot_motion_vs_time(trajectory)

        self.figure.tight_layout()
        self.canvas.draw()

    def add_reference_grid(self):
        """Add reference grid to the image."""
        # Add grid lines every 100 pixels
        for i in range(0, 512, 100):
            self.ax_main.axhline(y=i, color='cyan', alpha=0.3, linewidth=0.5)
            self.ax_main.axvline(x=i, color='cyan', alpha=0.3, linewidth=0.5)

    def plot_motion_trajectory(self, trajectory):
        """Plot motion trajectory."""
        x_drift = trajectory['x_drift']
        y_drift = trajectory['y_drift']

        if len(x_drift) > 0:
            # Plot trajectory
            self.ax_trajectory.plot(x_drift, y_drift, 'b-', linewidth=2, alpha=0.7)
            self.ax_trajectory.scatter(x_drift[0], y_drift[0], color='green', s=50,
                                     marker='o', label='Start', zorder=5)
            self.ax_trajectory.scatter(x_drift[-1], y_drift[-1], color='red', s=50,
                                     marker='s', label='End', zorder=5)

            # Add frame numbers along trajectory
            for i in range(0, len(x_drift), max(1, len(x_drift)//5)):
                self.ax_trajectory.annotate(f'{i}', (x_drift[i], y_drift[i]),
                                          xytext=(5, 5), textcoords='offset points',
                                          fontsize=8, alpha=0.7)

            self.ax_trajectory.set_xlabel('X Drift (pixels)')
            self.ax_trajectory.set_ylabel('Y Drift (pixels)')
            self.ax_trajectory.legend()
            self.ax_trajectory.grid(True, alpha=0.3)
            self.ax_trajectory.set_aspect('equal')

    def plot_motion_vectors(self, trajectory):
        """Plot motion vectors on the main image."""
        x_drift = trajectory['x_drift']
        y_drift = trajectory['y_drift']

        if len(x_drift) > 1:
            # Calculate motion vectors (differences between consecutive frames)
            dx = np.diff(x_drift)
            dy = np.diff(y_drift)

            # Plot vectors at regular intervals on the image
            step = max(1, len(dx) // 10)  # Show up to 10 vectors
            for i in range(0, len(dx), step):
                # Position vectors in image space
                x_pos = 50 + i * 400 / len(dx)  # Spread across image width
                y_pos = 50

                # Scale vectors for visibility
                scale = 10
                arrow = Arrow(x_pos, y_pos, dx[i] * scale, dy[i] * scale,
                            width=20, color='red', alpha=0.7)
                self.ax_main.add_patch(arrow)

    def plot_motion_vs_time(self, trajectory):
        """Plot motion vs time."""
        frames = trajectory['frames']
        total_drift = trajectory['total_drift']
        x_drift = trajectory['x_drift']
        y_drift = trajectory['y_drift']

        if len(frames) > 0:
            self.ax_time.plot(frames, total_drift, 'k-', linewidth=2, label='Total Drift')
            self.ax_time.plot(frames, np.abs(x_drift), 'r--', linewidth=1, label='X Drift', alpha=0.7)
            self.ax_time.plot(frames, np.abs(y_drift), 'b--', linewidth=1, label='Y Drift', alpha=0.7)

            self.ax_time.set_xlabel('Frame Number')
            self.ax_time.set_ylabel('Drift (pixels)')
            self.ax_time.legend()
            self.ax_time.grid(True, alpha=0.3)

    def update_statistics(self):
        """Update motion statistics display."""
        if not self.motion_data or self.current_tilt_idx >= len(self.motion_data['motion_stats']):
            return

        stats = self.motion_data['motion_stats'][self.current_tilt_idx]
        trajectory = self.motion_data['motion_trajectories'][self.current_tilt_idx]

        # Update statistics table
        self.stats_table.setRowCount(0)

        stats_data = [
            ("Tilt Angle", f"{stats['tilt_angle']:.1f}°"),
            ("Number of Frames", f"{stats['n_frames']}"),
            ("Total Motion", f"{stats['total_motion']:.2f} px"),
            ("Max Motion", f"{stats['max_motion']:.2f} px"),
            ("Mean Motion", f"{stats['mean_motion']:.2f} px"),
            ("Final X Drift", f"{trajectory['x_drift'][-1]:.2f} px" if len(trajectory['x_drift']) > 0 else "N/A"),
            ("Final Y Drift", f"{trajectory['y_drift'][-1]:.2f} px" if len(trajectory['y_drift']) > 0 else "N/A")
        ]

        for i, (param, value) in enumerate(stats_data):
            self.stats_table.insertRow(i)
            self.stats_table.setItem(i, 0, QTableWidgetItem(param))
            self.stats_table.setItem(i, 1, QTableWidgetItem(value))

        # Update quality assessment
        self.update_quality_assessment(stats)

    def update_quality_assessment(self, stats):
        """Update quality assessment display."""
        total_motion = stats['total_motion']

        # Simple quality assessment based on motion
        if total_motion < 5:
            quality = "Excellent"
            color = "green"
        elif total_motion < 15:
            quality = "Good"
            color = "blue"
        elif total_motion < 30:
            quality = "Fair"
            color = "orange"
        else:
            quality = "Poor"
            color = "red"

        quality_text = f"""
<b>Motion Quality Assessment:</b><br>
<span style="color: {color}"><b>{quality}</b></span><br><br>

<b>Assessment Criteria:</b><br>
• Total Motion: {total_motion:.2f} pixels<br>
• Threshold: &lt;5px (Excellent), &lt;15px (Good), &lt;30px (Fair)<br><br>

<b>Recommendations:</b><br>
"""

        if total_motion > 30:
            quality_text += "• Consider re-doing motion correction<br>"
            quality_text += "• Check sample stability<br>"
            quality_text += "• Verify acquisition parameters<br>"
        elif total_motion > 15:
            quality_text += "• Motion correction acceptable<br>"
            quality_text += "• Monitor for systematic drift<br>"
        else:
            quality_text += "• Excellent motion correction<br>"
            quality_text += "• Proceed with confidence<br>"

        self.quality_text.setHtml(quality_text)

    def analyze_motion(self):
        """Perform comprehensive motion analysis."""
        if not self.motion_data:
            return

        self.progress_bar.setVisible(True)
        self.status_label.setText("Analyzing motion patterns...")

        # Simulate analysis progress
        for i in range(101):
            self.progress_bar.setValue(i)
            QTimer.singleShot(10 * i, lambda: None)  # Simulate work

        self.progress_bar.setVisible(False)
        self.export_btn.setEnabled(True)
        self.status_label.setText("Motion analysis completed")

        # Calculate overall statistics
        all_stats = self.motion_data['motion_stats']
        analysis_results = {
            'mean_total_motion': np.mean([s['total_motion'] for s in all_stats]),
            'max_total_motion': np.max([s['total_motion'] for s in all_stats]),
            'mean_frames': np.mean([s['n_frames'] for s in all_stats]),
            'total_tilts': len(all_stats)
        }

        self.motion_analysis_updated.emit(analysis_results)

    def export_results(self):
        """Export motion analysis results."""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Motion Results", "",
            "CSV Files (*.csv);;Text Files (*.txt);;All Files (*)"
        )

        if file_path and self.motion_data:
            try:
                # Export to CSV
                import pandas as pd

                df = pd.DataFrame(self.motion_data['motion_stats'])
                df.to_csv(file_path, index=False)

                self.status_label.setText(f"Results exported to {Path(file_path).name}")
            except Exception as e:
                self.status_label.setText(f"Export failed: {e}")
