"""
Napari MRC Viewer with PyQt5 compatibility
Fixed to handle Qt backend conflicts properly.
"""

import logging
import os
import sys
from pathlib import Path
from typing import List, Optional, Union

logger = logging.getLogger(__name__)

# Set Qt backend before any Qt imports
os.environ["QT_API"] = "pyqt5"
os.environ["NAPARI_QT_BACKEND"] = "pyqt5"

# Force napari to use PyQt5
import napari

# Configure napari backend (handle different napari versions)
try:
    # Try new napari API
    if hasattr(napari, 'settings') and hasattr(napari.settings, 'get_settings'):
        napari.settings.get_settings().application.qt_backend = "pyqt5"
    else:
        # Environment variable is already set above
        pass
except (AttributeError, ImportError):
    # Fallback - environment variable should handle this
    pass

try:
    from PyQt6.QtCore import QThread, pyqtSignal, pyqtSlot
    from PyQt6.QtGui import QPixmap
    from PyQt6.QtWidgets import QHBoxLayout, QLabel, QPushButton, QVBoxLayout, QWidget
except ImportError as e:
    logger.error(f"PyQt5 import failed: {e}")
    raise

try:
    import mrcfile
    import napari.viewer
    import numpy as np
    from napari.qt import QtViewer
except ImportError as e:
    logger.error(f"Napari import failed: {e}")
    # Create a fallback viewer
    QtViewer = None
    napari = None

logger = logging.getLogger(__name__)


class NapariMRCViewer(QWidget):
    """Enhanced MRC viewer using Napari with PyQt5 compatibility."""

    # Signals
    file_loaded = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.napari_viewer = None
        self.current_file = None
        self.setup_ui()
        self.init_napari()

    def setup_ui(self):
        """Setup the UI layout."""
        layout = QVBoxLayout(self)

        # Control panel
        controls = QHBoxLayout()

        self.load_btn = QPushButton("Load MRC File")
        self.load_btn.clicked.connect(self.load_file_dialog)
        controls.addWidget(self.load_btn)

        self.info_label = QLabel("No file loaded")
        controls.addWidget(self.info_label)

        layout.addLayout(controls)

        # Napari viewer container
        self.viewer_container = QWidget()
        self.viewer_layout = QVBoxLayout(self.viewer_container)
        layout.addWidget(self.viewer_container)

    def init_napari(self):
        """Initialize Napari viewer with proper Qt backend."""
        try:
            # Ensure Qt backend is set correctly
            os.environ["QT_API"] = "pyqt6"

            if napari is None:
                logger.error("Napari not available, using fallback viewer")
                self.create_fallback_viewer()
                return

            # Create napari viewer with explicit Qt backend
            self.napari_viewer = napari.Viewer(show=False)

            # Get the Qt widget from napari
            napari_widget = self.napari_viewer.window._qt_window

            # Add to our layout
            self.viewer_layout.addWidget(napari_widget)

            logger.info("✅ Napari viewer initialized with PyQt5")

        except Exception as e:
            logger.error(f"Failed to initialize Napari viewer: {e}")
            self.create_fallback_viewer()

    def create_fallback_viewer(self):
        """Create a simple fallback viewer if Napari fails."""
        fallback_label = QLabel("Napari viewer not available\nUsing fallback display")
        fallback_label.setStyleSheet(
            "border: 1px solid gray; padding: 20px; text-align: center;"
        )
        self.viewer_layout.addWidget(fallback_label)
        logger.info("✅ Fallback viewer created")

    def load_file_dialog(self):
        """Open file dialog to load MRC file."""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load MRC File", "", "MRC Files (*.mrc *.st);;All Files (*)"
        )

        if file_path:
            self.load_mrc_file(file_path)

    def load_mrc_file(self, file_path: Union[str, Path]):
        """Load and display MRC file."""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")

            logger.info(f"Loading MRC file: {file_path}")

            # Read MRC file
            with mrcfile.open(file_path, mode="r") as mrc:
                data = mrc.data.copy()

            if self.napari_viewer is not None:
                # Clear existing layers
                self.napari_viewer.layers.clear()

                # Add data to napari
                if data.ndim == 3:
                    # 3D volume
                    self.napari_viewer.add_image(data, name=file_path.name)
                elif data.ndim == 2:
                    # 2D image
                    self.napari_viewer.add_image(data, name=file_path.name)
                else:
                    raise ValueError(f"Unsupported data dimensions: {data.ndim}")

                # Update info
                self.info_label.setText(f"Loaded: {file_path.name} ({data.shape})")
                self.current_file = str(file_path)
                self.file_loaded.emit(str(file_path))

                logger.info(f"✅ Successfully loaded {file_path.name}")
            else:
                # Fallback: just update info
                self.info_label.setText(
                    f"File: {file_path.name} ({data.shape}) - Napari not available"
                )
                logger.info(f"✅ File info loaded (fallback mode): {file_path.name}")

        except Exception as e:
            error_msg = f"Failed to load MRC file: {e}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            self.info_label.setText("Error loading file")

    def get_current_data(self):
        """Get currently displayed data."""
        if self.napari_viewer and len(self.napari_viewer.layers) > 0:
            return self.napari_viewer.layers[0].data
        return None

    def clear_viewer(self):
        """Clear the viewer."""
        if self.napari_viewer:
            self.napari_viewer.layers.clear()
        self.info_label.setText("No file loaded")
        self.current_file = None

    def closeEvent(self, event):
        """Handle close event."""
        if self.napari_viewer:
            try:
                self.napari_viewer.close()
            except:
                pass
        super().closeEvent(event)


# Compatibility function for older imports
def create_napari_viewer(parent=None):
    """Create a Napari MRC viewer instance."""
    return NapariMRCViewer(parent)
