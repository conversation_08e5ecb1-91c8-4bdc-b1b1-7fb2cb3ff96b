#!/usr/bin/env python3
"""
AreTomo3 GUI - Rich Styled Version
A professional GUI with modern Qt styling
"""

import sys
import os
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

# PyQt6 imports
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QLabel, QLineEdit, QPushButton, QTextEdit, QStatusBar,
    QFileDialog, QMessageBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QGroupBox, QFormLayout, QMenuBar, QApplication, QComboBox,
    QSplitter, QScrollArea, QFrame
)
from PyQt6.QtCore import Qt, QTimer, QUrl
from PyQt6.QtGui import QAction, QFont, QIcon

# Import theme manager
from .themes import ThemeManager

# Set up logging
logger = logging.getLogger(__name__)


class RichAreTomoGUI(QMainWindow):
    """Rich styled AreTomo3 GUI with professional appearance"""
    
    def __init__(self):
        super().__init__()
        logger.info("Initializing Rich AreTomo3 GUI...")

        # Initialize theme manager
        self.theme_manager = ThemeManager()

        # Initialize web server properties
        self.web_server = None
        self.web_server_port = 8000
        self.web_api = None

        # Initialize auto-plot generation
        self.auto_plot_enabled = True
        self.plot_server = None
        self.realtime_monitor = None

        # Apply styling based on current theme
        self.apply_theme()

        # Initialize UI
        self.init_ui()

        # Set up status bar
        self.setup_status_bar()

        # Initialize auto-plot generation
        self.setup_auto_plot_generation()

        logger.info("Rich AreTomo3 GUI initialized successfully")

    def setup_auto_plot_generation(self):
        """Setup automatic plot generation system."""
        try:
            # Initialize plot server
            from ..web.plot_server import start_plot_server
            self.plot_server = start_plot_server(host="0.0.0.0", port=8001)
            logger.info("Plot server started on http://0.0.0.0:8001")

            # Initialize real-time monitor
            from ..analysis.realtime_monitor import RealtimeProcessingMonitor
            self.realtime_monitor = RealtimeProcessingMonitor(
                theme=self.theme_manager.get_current_theme(),
                auto_plot=self.auto_plot_enabled
            )

            # Add completion callback to register plots with web server
            self.realtime_monitor.add_completion_callback(self._on_processing_completion)

            logger.info("Auto-plot generation system initialized")

        except Exception as e:
            logger.error(f"Failed to setup auto-plot generation: {e}")
            self.auto_plot_enabled = False

    def _on_processing_completion(self, output_dir: str, data: dict):
        """Handle processing completion events."""
        try:
            dataset_id = os.path.basename(output_dir.rstrip('/'))
            analysis_data = data.get('analysis_data', {})
            generated_plots = data.get('generated_plots', {})

            logger.info(f"Processing completed for {dataset_id}: {len(generated_plots)} plots generated")

            # Register with plot server
            if self.plot_server and generated_plots:
                self.plot_server.register_dataset(
                    dataset_id=dataset_id,
                    output_dir=output_dir,
                    plots=generated_plots,
                    analysis_data=analysis_data
                )
                logger.info(f"Registered {dataset_id} with plot server")

            # Update GUI log
            if hasattr(self, 'log_text') and self.log_text:
                self.log_text.append(f"🎉 [{dataset_id}] Processing complete! {len(generated_plots)} plots generated and available via web interface.")

        except Exception as e:
            logger.error(f"Error handling processing completion: {e}")

    def apply_theme(self):
        """Apply the current theme to the GUI"""
        # Set window properties
        self.setWindowTitle("AreTomo3-GUI")  # Remove emoji from title
        self.setMinimumSize(1200, 800)
        self.setGeometry(100, 100, 1400, 900)

        # Enable window controls (minimize, maximize, close)
        self.setWindowFlags(
            Qt.WindowType.Window |
            Qt.WindowType.WindowMinimizeButtonHint |
            Qt.WindowType.WindowMaximizeButtonHint |
            Qt.WindowType.WindowCloseButtonHint
        )

        # Apply theme stylesheet
        stylesheet = self.theme_manager.get_theme_stylesheet()
        self.setStyleSheet(stylesheet)

        # Apply plot theme as well
        try:
            from .plot_theme_manager import set_plot_theme
            current_theme = self.theme_manager.get_current_theme()
            set_plot_theme(current_theme)
            logger.info(f"Applied {current_theme} theme to GUI and plots")
        except ImportError:
            logger.warning("Plot theme manager not available")
            current_theme = self.theme_manager.get_current_theme()
            logger.info(f"Applied {current_theme} theme to GUI only")

        logger.info(f"Applied {self.theme_manager.get_current_theme()} theme")

    def toggle_theme(self):
        """Toggle between light and dark themes"""
        current_theme = self.theme_manager.get_current_theme()
        new_theme = "dark" if current_theme == "light" else "light"
        self.theme_manager.set_theme(new_theme)
        self.apply_theme()
        logger.info(f"Switched to {new_theme} theme")

    def toggle_theme_and_update_menu(self):
        """Toggle theme and update menu text"""
        self.toggle_theme()
        # Update menu text
        current_theme = self.theme_manager.get_current_theme()
        new_text = f"🎨 Switch to {'Dark' if current_theme == 'light' else 'Light'} Theme"
        self.theme_action.setText(new_text)

    def init_ui(self):
        """Initialize the user interface"""
        # Create menu bar
        self.create_menu_bar()
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.tab_widget.setMovable(True)
        layout.addWidget(self.tab_widget)
        
        # Create all comprehensive tabs
        self.create_configuration_tab()  # Renamed from main_tab - includes system monitor
        self.create_aretomo3_parameters_tab()  # Renamed from enhanced_parameters_tab
        self.create_batch_processing_tab()
        self.create_live_processing_tab()
        self.create_unified_analysis_tab()  # Replaces enhanced + realtime analysis tabs (includes CTF & Motion viewers)
        self.create_napari_viewer_tab()  # Renamed from viewer_tab
        self.create_export_tab()
        self.create_log_tab()  # System logs and recent files
        self.create_web_dashboard_tab()  # New web dashboard integration
        # System monitor moved under configuration tab
        # CTF & Motion viewers integrated within Analysis Workbench tab
        
    def create_menu_bar(self):
        """Create professional menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("📁 File")
        
        new_action = QAction("🆕 New Project", self)
        new_action.setShortcut("Ctrl+N")
        file_menu.addAction(new_action)
        
        open_action = QAction("📂 Open Project", self)
        open_action.setShortcut("Ctrl+O")
        file_menu.addAction(open_action)
        
        save_action = QAction("💾 Save Project", self)
        save_action.setShortcut("Ctrl+S")
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("🚪 Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("🔧 Tools")

        # Theme toggle
        theme_action = QAction(f"🎨 Switch to {'Dark' if self.theme_manager.get_current_theme() == 'light' else 'Light'} Theme", self)
        theme_action.setShortcut("Ctrl+T")
        theme_action.triggered.connect(self.toggle_theme_and_update_menu)
        tools_menu.addAction(theme_action)
        self.theme_action = theme_action  # Store reference to update text

        tools_menu.addSeparator()

        settings_action = QAction("⚙️ Settings", self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # Help menu
        help_menu = menubar.addMenu("❓ Help")
        
        about_action = QAction("ℹ️ About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_configuration_tab(self):
        """Create configuration tab with system monitor below settings"""
        try:
            # Create main configuration tab
            config_main_tab = QWidget()
            config_layout = QVBoxLayout(config_main_tab)
            config_layout.setSpacing(10)
            config_layout.setContentsMargins(10, 10, 10, 10)

            # Configuration settings section (top)
            from .tabs.main_tab import MainTabManager

            # Create a container for the main tab content
            config_settings_container = QWidget()
            config_settings_layout = QVBoxLayout(config_settings_container)

            self.main_tab_manager = MainTabManager(self)
            self.main_tab_manager.setup_tab(config_settings_container, config_settings_layout)

            # Add configuration settings to main layout
            config_layout.addWidget(config_settings_container)

            # Add separator
            separator = QFrame()
            separator.setFrameShape(QFrame.Shape.HLine)
            separator.setFrameShadow(QFrame.Shadow.Sunken)
            separator.setStyleSheet("color: #ccc; margin: 10px 0;")
            config_layout.addWidget(separator)

            # System monitor section (bottom)
            try:
                from .tabs.enhanced_monitor_tab import EnhancedMonitorTab

                # Create system monitor with reduced height
                self.enhanced_monitor_tab = EnhancedMonitorTab(self)
                self.enhanced_monitor_tab.setMaximumHeight(400)  # Limit height to fit nicely

                # Add section header
                monitor_header = QLabel("📊 System Monitor")
                monitor_header.setStyleSheet("font-size: 16px; font-weight: bold; padding: 5px; color: #333;")
                config_layout.addWidget(monitor_header)

                config_layout.addWidget(self.enhanced_monitor_tab)

            except ImportError as e:
                logger.warning(f"Could not import EnhancedMonitorTab: {e}")
                # Create simple monitor fallback
                monitor_header = QLabel("📊 System Monitor")
                monitor_header.setStyleSheet("font-size: 16px; font-weight: bold; padding: 5px; color: #333;")
                config_layout.addWidget(monitor_header)

                monitor_label = QLabel("System monitoring will be available when dependencies are installed.")
                monitor_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                monitor_label.setStyleSheet("color: #888; padding: 20px; border: 1px dashed #ccc; border-radius: 5px;")
                monitor_label.setMaximumHeight(100)
                config_layout.addWidget(monitor_label)

            # Set stretch factors to balance space
            config_layout.setStretchFactor(config_settings_container, 1)
            if hasattr(self, 'enhanced_monitor_tab'):
                config_layout.setStretchFactor(self.enhanced_monitor_tab, 1)

            self.tab_widget.addTab(config_main_tab, "Project Setup")

        except ImportError as e:
            logger.warning(f"Could not import MainTabManager: {e}")
            # Fallback to simple tab
            self.create_simple_configuration_tab()

    def create_aretomo3_parameters_tab(self):
        """Create AreTomo3 parameters tab (renamed from enhanced parameters)"""
        try:
            from .tabs.enhanced_parameters_tab import EnhancedParametersTab

            # Create the AreTomo3 parameters tab
            self.aretomo3_params_tab = EnhancedParametersTab(self)
            self.tab_widget.addTab(self.aretomo3_params_tab, "Reconstruction Parameters")

        except ImportError as e:
            logger.warning(f"Could not import EnhancedParametersTab: {e}")
            # Fallback to simple parameters tab
            self.create_simple_aretomo3_parameters_tab()

    def create_batch_processing_tab(self):
        """Create comprehensive batch processing tab"""
        try:
            from .tabs.batch_tab import BatchTabManager

            tab = QWidget()
            layout = QVBoxLayout(tab)

            # Use the comprehensive batch tab manager
            self.batch_tab_manager = BatchTabManager(self)
            self.batch_tab_manager.setup_tab(tab, layout)

            self.tab_widget.addTab(tab, "Batch Manager")

        except ImportError as e:
            logger.warning(f"Could not import BatchTabManager: {e}")
            self.create_simple_batch_tab()

    def create_live_processing_tab(self):
        """Create comprehensive live processing tab"""
        try:
            from .tabs.live_processing_tab import LiveProcessingTab

            # Create the live processing tab
            self.live_processing_tab = LiveProcessingTab(self)
            self.tab_widget.addTab(self.live_processing_tab, "Live Monitor")

        except ImportError as e:
            logger.warning(f"Could not import LiveProcessingTab: {e}")
            self.create_simple_live_tab()

    # Enhanced Analysis Tab removed - merged into Unified Analysis Tab

    def create_unified_analysis_tab(self):
        """Create unified analysis tab (replaces enhanced + realtime analysis)"""
        try:
            from .tabs.unified_analysis_tab import UnifiedAnalysisTab

            # Create the unified analysis tab (includes embedded CTF & Motion viewers)
            self.unified_analysis_tab = UnifiedAnalysisTab(self)
            self.tab_widget.addTab(self.unified_analysis_tab, "🔬 Analysis Workbench")

            logger.info("Unified analysis tab created successfully")

        except ImportError as e:
            logger.warning(f"Could not import UnifiedAnalysisTab: {e}")
            self.create_simple_unified_analysis_tab()

    def create_napari_viewer_tab(self):
        """Create Napari integration viewer tab"""
        try:
            from .tabs.napari_viewer_tab import NapariViewerTab

            # Create the Napari viewer tab
            self.napari_viewer_tab = NapariViewerTab(self)
            self.tab_widget.addTab(self.napari_viewer_tab, "Volume Viewer")

        except ImportError as e:
            logger.warning(f"Could not import NapariViewerTab: {e}")
            self.create_simple_napari_viewer_tab()

    def create_export_tab(self):
        """Create comprehensive export tab"""
        try:
            from .tabs.export_tab import ExportTabManager

            tab = QWidget()
            layout = QVBoxLayout(tab)

            # Use the comprehensive export tab manager
            self.export_tab_manager = ExportTabManager(self)
            self.export_tab_manager.setup_tab(tab, layout)

            self.tab_widget.addTab(tab, "Export Tools")

        except ImportError as e:
            logger.warning(f"Could not import ExportTabManager: {e}")
            self.create_simple_export_tab()

    def create_log_tab(self):
        """Create comprehensive log tab with system logs and recent files."""
        try:
            from .tabs.log_tab import LogTabManager

            log_tab = QWidget()
            layout = QVBoxLayout(log_tab)
            layout.setContentsMargins(15, 15, 15, 15)
            layout.setSpacing(15)

            # Initialize log tab manager
            self.log_tab_manager = LogTabManager(self)
            self.log_tab_manager.setup_tab(log_tab, layout)

            # Connect to logging system
            self._setup_log_handler()

            self.tab_widget.addTab(log_tab, "System Status")
            logger.info("Log tab created successfully")

        except Exception as e:
            logger.error(f"Error creating log tab: {e}")
            # Create fallback simple log tab
            self._create_simple_log_tab()

    def _create_simple_log_tab(self):
        """Create a simple fallback log tab."""
        log_tab = QWidget()
        layout = QVBoxLayout(log_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # Header
        header = QLabel("📋 System Logs")
        header.setObjectName("header")
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(header)

        # Log controls
        controls_layout = QHBoxLayout()

        clear_btn = QPushButton("🗑️ Clear Logs")
        clear_btn.clicked.connect(self._clear_logs)
        controls_layout.addWidget(clear_btn)

        save_btn = QPushButton("💾 Save Logs")
        save_btn.clicked.connect(self._save_logs)
        controls_layout.addWidget(save_btn)

        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self._refresh_logs)
        controls_layout.addWidget(refresh_btn)

        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        # Log display with white background
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Courier New', monospace;
                font-size: 11px;
                background-color: #ffffff;
                color: #000000;
                border: 1px solid #cccccc;
                border-radius: 5px;
                padding: 10px;
                selection-background-color: #3399ff;
                selection-color: #ffffff;
            }
        """)

        # Add initial log messages
        from datetime import datetime
        self.log_text.append("🚀 AreTomo3-GUI System Logs")
        self.log_text.append("=" * 50)
        self.log_text.append(f"📅 Session started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.log_text.append("✅ GUI initialized successfully")
        self.log_text.append("📋 Log system ready")
        self.log_text.append("🔧 Setting up log handler...")
        self.log_text.append("")

        layout.addWidget(self.log_text)

        self.tab_widget.addTab(log_tab, "📋 System Logs")

    def _setup_log_handler(self):
        """Set up log handler to capture application logs."""
        try:
            # Create custom log handler
            class GuiLogHandler(logging.Handler):
                def __init__(self, log_widget):
                    super().__init__()
                    self.log_widget = log_widget

                def emit(self, record):
                    try:
                        msg = self.format(record)
                        from datetime import datetime
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        level_icon = {
                            'DEBUG': '🔍',
                            'INFO': 'ℹ️',
                            'WARNING': '⚠️',
                            'ERROR': '❌',
                            'CRITICAL': '🚨'
                        }.get(record.levelname, '📝')

                        formatted_msg = f"[{timestamp}] {level_icon} {msg}"

                        # Add to log widget (thread-safe using QTimer)
                        if hasattr(self.log_widget, 'append'):
                            from PyQt6.QtCore import QTimer
                            QTimer.singleShot(0, lambda: self.log_widget.append(formatted_msg))
                    except Exception:
                        pass  # Ignore logging errors

            # Add handler to multiple loggers
            # Check for both log_text (simple tab) and log_viewer (LogTabManager)
            log_widget = None
            if hasattr(self, 'log_tab_manager') and hasattr(self.log_tab_manager, 'log_viewer'):
                log_widget = self.log_tab_manager.log_viewer
            elif hasattr(self, 'log_text'):
                log_widget = self.log_text

            if log_widget:
                self.gui_log_handler = GuiLogHandler(log_widget)
                self.gui_log_handler.setLevel(logging.INFO)
                formatter = logging.Formatter('%(message)s')  # Simplified format
                self.gui_log_handler.setFormatter(formatter)

                # Add to root logger to catch all logs
                root_logger = logging.getLogger()
                root_logger.addHandler(self.gui_log_handler)

                # Add to specific loggers
                aretomo_logger = logging.getLogger('aretomo3_gui')
                aretomo_logger.addHandler(self.gui_log_handler)

                # Set logging level to capture more messages
                aretomo_logger.setLevel(logging.INFO)

                # Test the log handler
                logger.info("Log handler setup completed - logs should now appear in GUI")

                # Add initial messages to the log widget
                if hasattr(log_widget, 'append'):
                    from datetime import datetime
                    log_widget.append("🚀 AreTomo3-GUI System Logs")
                    log_widget.append("=" * 50)
                    log_widget.append(f"📅 Session started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    log_widget.append("✅ GUI initialized successfully")
                    log_widget.append("📋 Log system ready")
                    log_widget.append("🔧 Log handler connected successfully")
                    log_widget.append("")

        except Exception as e:
            logger.error(f"Error setting up log handler: {e}")

    def _clear_logs(self):
        """Clear the log display."""
        if hasattr(self, 'log_text'):
            self.log_text.clear()
            self.log_text.append("📋 Logs cleared")
            logger.info("Log display cleared")

    def _save_logs(self):
        """Save current logs to file."""
        try:
            if hasattr(self, 'log_text'):
                from datetime import datetime
                filename, _ = QFileDialog.getSaveFileName(
                    self,
                    "Save System Logs",
                    f"aretomo3_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                    "Text Files (*.txt);;All Files (*)"
                )
                if filename:
                    with open(filename, 'w') as f:
                        f.write(self.log_text.toPlainText())
                    QMessageBox.information(self, "Logs Saved", f"Logs saved to:\n{filename}")
                    logger.info(f"Logs saved to {filename}")
        except Exception as e:
            QMessageBox.warning(self, "Save Error", f"Error saving logs:\n{str(e)}")

    def _refresh_logs(self):
        """Refresh log display."""
        if hasattr(self, 'log_text'):
            from datetime import datetime
            self.log_text.append(f"🔄 Logs refreshed at {datetime.now().strftime('%H:%M:%S')}")
            logger.info("Log display refreshed")

    def add_log_message(self, message: str, level: str = "INFO"):
        """Add a message to the log display."""
        if hasattr(self, 'log_text'):
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
            level_icon = {
                'DEBUG': '🔍',
                'INFO': 'ℹ️',
                'WARNING': '⚠️',
                'ERROR': '❌',
                'CRITICAL': '🚨'
            }.get(level.upper(), '📝')

            formatted_msg = f"[{timestamp}] {level_icon} {message}"
            self.log_text.append(formatted_msg)

    def create_web_dashboard_tab(self):
        """Create web dashboard tab for remote access and monitoring"""
        try:
            from PyQt6.QtWebEngineWidgets import QWebEngineView
            from PyQt6.QtCore import QUrl

            # Create main web dashboard tab
            web_tab = QWidget()
            web_layout = QVBoxLayout(web_tab)

            # Header with controls
            header_layout = QHBoxLayout()

            # Server status
            self.web_server_status = QLabel("🔴 Web Server: Stopped")
            self.web_server_status.setStyleSheet("font-weight: bold; padding: 5px;")
            header_layout.addWidget(self.web_server_status)

            # Start/Stop server button
            self.web_server_btn = QPushButton("🚀 Start Web Server")
            self.web_server_btn.clicked.connect(self.toggle_web_server)
            header_layout.addWidget(self.web_server_btn)

            # Server URL
            self.web_url_label = QLabel("URL: http://localhost:8000")
            self.web_url_label.setStyleSheet("color: #666; font-family: monospace;")
            header_layout.addWidget(self.web_url_label)

            # Open in browser button
            self.open_browser_btn = QPushButton("🌐 Open in Browser")
            self.open_browser_btn.clicked.connect(self.open_web_dashboard)
            self.open_browser_btn.setEnabled(False)
            header_layout.addWidget(self.open_browser_btn)

            header_layout.addStretch()
            web_layout.addLayout(header_layout)

            # Embedded web view
            try:
                self.web_view = QWebEngineView()
                self.web_view.setUrl(QUrl("about:blank"))
                web_layout.addWidget(self.web_view)

                # Load initial page
                initial_html = """
                <html>
                <head>
                    <title>AreTomo3 Web Dashboard</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            text-align: center;
                            padding: 50px;
                            margin: 0;
                        }
                        .container {
                            background: rgba(255,255,255,0.1);
                            border-radius: 15px;
                            padding: 40px;
                            backdrop-filter: blur(10px);
                        }
                        h1 { color: #fff; margin-bottom: 20px; }
                        p { font-size: 18px; margin: 15px 0; }
                        .status {
                            background: rgba(255,255,255,0.2);
                            padding: 15px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🔬 AreTomo3 Web Dashboard</h1>
                        <p>Advanced web interface for remote monitoring and control</p>
                        <div class="status">
                            <h3>🔴 Server Status: Stopped</h3>
                            <p>Click "Start Web Server" to enable remote access</p>
                        </div>
                        <h3>Features:</h3>
                        <p>• Real-time processing monitoring</p>
                        <p>• Interactive analysis dashboard</p>
                        <p>• Remote job submission</p>
                        <p>• Live system performance metrics</p>
                        <p>• WebSocket real-time updates</p>
                        <p>• Secure API access</p>
                    </div>
                </body>
                </html>
                """
                self.web_view.setHtml(initial_html)

            except ImportError:
                # Fallback if QWebEngineView not available
                fallback_label = QLabel(
                    "🌐 Web Dashboard\n\n"
                    "Web dashboard provides remote access to AreTomo3 GUI.\n\n"
                    "Features:\n"
                    "• Real-time processing monitoring\n"
                    "• Interactive analysis dashboard\n"
                    "• Remote job submission\n"
                    "• Live system performance metrics\n"
                    "• WebSocket real-time updates\n"
                    "• Secure API access\n\n"
                    "Install PyQt6-WebEngine for embedded view:\n"
                    "pip install PyQt6-WebEngine"
                )
                fallback_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                fallback_label.setStyleSheet(
                    "border: 2px dashed #ccc; padding: 50px; "
                    "font-size: 14px; color: #666; background: #f9f9f9;"
                )
                web_layout.addWidget(fallback_label)

            self.tab_widget.addTab(web_tab, "Web Portal")

            # Initialize web server (but don't start it)
            self.web_server = None
            self.web_server_port = 8000

            # Initialize shared web server status
            self.init_web_server_status_sync()

        except Exception as e:
            logger.warning(f"Could not create web dashboard tab: {e}")
            self.create_simple_web_tab()

    def toggle_web_server(self):
        """Toggle web server on/off"""
        if self.web_server is None:
            self.start_web_server()
        else:
            self.stop_web_server()

    def start_web_server(self):
        """Start the web server"""
        try:
            from ..web.api_server import AreTomo3WebAPI
            from ..core.realtime_processor import RealTimeProcessor
            from pathlib import Path

            # Create processor if not exists
            if not hasattr(self, 'realtime_processor'):
                # Get watch directories and output directory from GUI
                # IMPORTANT: Watch directory should be OUTPUT directory, not input!
                watch_directories = []
                output_directory = Path.home() / "aretomo3_output"

                # Try to get output directory from GUI (this becomes both output AND watch directory)
                if hasattr(self, 'output_dir') and self.output_dir.text().strip():
                    output_directory = Path(self.output_dir.text().strip())
                    # Watch the output directory for completed AreTomo3 results
                    watch_directories.append(output_directory)

                # If no output directory specified, use default
                if not watch_directories:
                    watch_directories = [output_directory]

                # Create directories if they don't exist
                for watch_dir in watch_directories:
                    watch_dir.mkdir(parents=True, exist_ok=True)
                output_directory.mkdir(parents=True, exist_ok=True)

                logger.info(f"Creating RealTimeProcessor with watch_directories: {watch_directories}, output_directory: {output_directory}")
                self.realtime_processor = RealTimeProcessor(watch_directories, output_directory)

            # Create web API
            self.web_api = AreTomo3WebAPI(self.realtime_processor)
            self.web_api.set_main_window(self)

            # Start server in background thread
            import threading
            import uvicorn

            def run_server():
                uvicorn.run(
                    self.web_api.app,
                    host="0.0.0.0",
                    port=self.web_server_port,
                    log_level="info"
                )

            self.web_server_thread = threading.Thread(target=run_server, daemon=True)
            self.web_server_thread.start()
            self.web_server = True

            # Sync status across all tabs
            self.sync_web_server_status("🟢 Web Server: Running", True)
            self.open_browser_btn.setEnabled(True)

            # Load dashboard in embedded view
            if hasattr(self, 'web_view'):
                self.web_view.setUrl(QUrl(f"http://localhost:{self.web_server_port}"))

            logger.info(f"Web server started on port {self.web_server_port}")

        except Exception as e:
            logger.error(f"Failed to start web server: {e}")
            self.sync_web_server_status("🔴 Web Server: Error", False)
            QMessageBox.warning(
                self, "Web Server Error",
                f"Failed to start web server:\n{str(e)}\n\n"
                "Make sure required packages are installed:\n"
                "pip install fastapi uvicorn"
            )

    def stop_web_server(self):
        """Stop the web server"""
        self.web_server = None
        self.sync_web_server_status("🔴 Web Server: Stopped", False)
        self.open_browser_btn.setEnabled(False)

        # Reset embedded view
        if hasattr(self, 'web_view'):
            self.web_view.setUrl(QUrl("about:blank"))

        logger.info("Web server stopped")

    def open_web_dashboard(self):
        """Open web dashboard in external browser - enhanced version"""
        if hasattr(self, 'web_server') and self.web_server:
            import webbrowser
            webbrowser.open(f"http://localhost:{self.web_server_port}")
        else:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(
                self, "Web Server Not Running",
                "Please start the web server first by clicking 'Start Web Server'."
            )

    def check_web_dependencies(self):
        """Check if web dashboard dependencies are available"""
        missing_deps = []
        try:
            import fastapi
            import uvicorn
        except ImportError:
            missing_deps.append("fastapi uvicorn")

        try:
            from PyQt6.QtWebEngineWidgets import QWebEngineView
        except ImportError:
            missing_deps.append("PyQt6-WebEngine")

        return missing_deps

    def install_web_dependencies(self):
        """Show instructions for installing web dependencies"""
        missing_deps = self.check_web_dependencies()
        if missing_deps:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(
                self, "Web Dashboard Dependencies",
                f"To enable full web dashboard functionality, install:\n\n"
                f"pip install {' '.join(missing_deps)}\n\n"
                "Then restart the application for full integration."
            )
            return False
        return True

    def create_simple_web_tab(self):
        """Create simple web tab fallback"""
        web_tab = QWidget()
        layout = QVBoxLayout(web_tab)

        label = QLabel(
            "🌐 Web Dashboard\n\n"
            "Advanced web interface for remote monitoring.\n"
            "Install required packages to enable:\n\n"
            "pip install fastapi uvicorn PyQt6-WebEngine"
        )
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: #888; padding: 50px;")
        layout.addWidget(label)

        self.tab_widget.addTab(web_tab, "Web Portal")

    def init_web_server_status_sync(self):
        """Initialize web server status synchronization between tabs."""
        # List of all web server status labels to sync
        self.web_status_labels = []
        self.web_server_buttons = []

        # Collect status labels from different tabs
        if hasattr(self, 'web_server_status'):
            self.web_status_labels.append(self.web_server_status)

        if hasattr(self, 'web_server_btn'):
            self.web_server_buttons.append(self.web_server_btn)

        # Check Project Setup tab for web server controls
        if hasattr(self, 'main_tab_manager'):
            main_tab = self.main_tab_manager
            if hasattr(main_tab, 'web_server_status'):
                self.web_status_labels.append(main_tab.web_server_status)
            if hasattr(main_tab, 'web_server_btn'):
                self.web_server_buttons.append(main_tab.web_server_btn)

        # Initial status sync
        self.sync_web_server_status("🔴 Web Server: Stopped", False)

    def sync_web_server_status(self, status_text: str, is_running: bool):
        """Sync web server status across all tabs."""
        # Update all status labels
        for label in self.web_status_labels:
            if label:
                label.setText(status_text)
                if is_running:
                    label.setStyleSheet("font-weight: bold; color: #28a745; padding: 5px;")
                else:
                    label.setStyleSheet("font-weight: bold; color: #dc3545; padding: 5px;")

        # Update all buttons
        for button in self.web_server_buttons:
            if button:
                if is_running:
                    button.setText("🛑 Stop Web Server")
                    button.setStyleSheet("""
                        QPushButton {
                            background-color: #dc3545;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #c82333;
                        }
                    """)
                else:
                    button.setText("🚀 Start Web Server")
                    button.setStyleSheet("""
                        QPushButton {
                            background-color: #28a745;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #218838;
                        }
                    """)

    # System monitor moved to Configuration tab as sub-tab

    def setup_status_bar(self):
        """Setup professional status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("🚀 AreTomo3-GUI - Ready")

    # Event handlers
    def browse_input_dir(self):
        """Browse for input directory with enhanced directory structure setup"""
        dir_path = QFileDialog.getExistingDirectory(self, "Select Input Directory")
        if dir_path:
            self.input_dir.setText(dir_path)

            # Auto-generate output directory if enabled
            if hasattr(self, 'auto_generate_output') and self.auto_generate_output.isChecked():
                self.update_output_directory_from_input(dir_path)

            # Update directory preview
            self.update_directory_preview()

            self.add_log_message(f"Input directory set: {dir_path}", "INFO")

            # Create directory structure if requested
            if hasattr(self, 'monitor_inside_input') and self.monitor_inside_input.isChecked():
                self.create_directory_structure(dir_path)

    def browse_output_dir(self):
        """Browse for output directory manually"""
        dir_path = QFileDialog.getExistingDirectory(self, "Select Output Directory")
        if dir_path:
            self.output_dir.setText(dir_path)

            # Disable auto-generate when manually selected
            if hasattr(self, 'auto_generate_output'):
                self.auto_generate_output.setChecked(False)

            # Update directory preview
            self.update_directory_preview()

            self.add_log_message(f"Output directory set: {dir_path}", "INFO")

    def on_auto_generate_output_toggled(self, checked):
        """Handle auto-generate output directory toggle"""
        if checked and hasattr(self, 'input_dir') and self.input_dir.text():
            # Re-generate output directory from input
            self.update_output_directory_from_input(self.input_dir.text())

        # Update directory preview
        self.update_directory_preview()

        # Enable/disable manual browse button
        if hasattr(self, 'output_browse_btn'):
            # Find the browse button (it's in the layout)
            pass  # Button state handled by the checkbox logic

    def update_output_directory_from_input(self, input_dir):
        """Update output directory based on input directory and settings"""
        if not input_dir:
            return

        # Get output folder name
        folder_name = "aretomo3_output"
        if hasattr(self, 'output_folder_name') and self.output_folder_name.text().strip():
            folder_name = self.output_folder_name.text().strip()

        # Create output path inside input directory
        output_path = os.path.join(input_dir, folder_name)
        self.output_dir.setText(output_path)

        self.add_log_message(f"Output directory auto-generated: {output_path}", "INFO")

    def create_directory_structure(self, input_dir):
        """Create the complete directory structure inside input folder"""
        try:
            input_path = Path(input_dir)

            # Get output folder name
            folder_name = "aretomo3_output"
            if hasattr(self, 'output_folder_name') and self.output_folder_name.text().strip():
                folder_name = self.output_folder_name.text().strip()

            # Create directories
            directories_to_create = [
                input_path / folder_name,  # Main output directory
                input_path / "monitor",    # Monitor directory
                input_path / "watch",      # Watch directory
                input_path / "logs",       # Logs directory
                input_path / "temp",       # Temporary files
            ]

            created_dirs = []
            for directory in directories_to_create:
                if not directory.exists():
                    directory.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(str(directory))
                    self.add_log_message(f"Created directory: {directory}", "INFO")

            if created_dirs:
                self.add_log_message(f"Directory structure created successfully", "INFO")
            else:
                self.add_log_message(f"Directory structure already exists", "INFO")

        except Exception as e:
            self.add_log_message(f"Error creating directory structure: {e}", "ERROR")

    def update_directory_preview(self):
        """Update the directory structure preview"""
        try:
            if not hasattr(self, 'directory_preview'):
                return

            input_dir = self.input_dir.text() if hasattr(self, 'input_dir') else ""
            output_dir = self.output_dir.text() if hasattr(self, 'output_dir') else ""

            if not input_dir:
                self.directory_preview.setText("Select an input directory to see structure preview")
                return

            # Get settings
            auto_generate = getattr(self, 'auto_generate_output', None)
            monitor_inside = getattr(self, 'monitor_inside_input', None)
            folder_name = getattr(self, 'output_folder_name', None)

            auto_gen_checked = auto_generate.isChecked() if auto_generate else True
            monitor_checked = monitor_inside.isChecked() if monitor_inside else True
            output_folder = folder_name.text() if folder_name and folder_name.text().strip() else "aretomo3_output"

            # Build preview text
            preview_lines = [
                f"📁 {os.path.basename(input_dir)}/ (Input Directory)",
                f"├── 📄 *.eer, *.tif, *.mdoc (Raw data files)",
            ]

            if auto_gen_checked:
                preview_lines.extend([
                    f"├── 📁 {output_folder}/ (AreTomo3 Output)",
                    f"│   ├── 📄 *_rec.mrc (Reconstructed tomograms)",
                    f"│   ├── 📄 *_ali.mrc (Aligned tilt series)",
                    f"│   ├── 📄 *.ctf (CTF estimation results)",
                    f"│   └── 📄 *.log (Processing logs)",
                ])
            else:
                preview_lines.append(f"├── 📁 Output: {output_dir}")

            if monitor_checked:
                preview_lines.extend([
                    f"├── 📁 monitor/ (Real-time monitoring)",
                    f"├── 📁 watch/ (File watching)",
                    f"├── 📁 logs/ (System logs)",
                    f"└── 📁 temp/ (Temporary files)",
                ])

            preview_text = "\n".join(preview_lines)
            self.directory_preview.setText(preview_text)

        except Exception as e:
            if hasattr(self, 'directory_preview'):
                self.directory_preview.setText(f"Error generating preview: {e}")

    def browse_aretomo_path(self):
        """Browse for AreTomo3 executable"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select AreTomo3 Executable", "", "Executable Files (*)"
        )
        if file_path:
            self.aretomo_path.setText(file_path)
            self.log_text.append(f"⚙️ AreTomo3 path set: {file_path}")

    def load_tilt_series(self):
        """Load tilt series with enhanced functionality"""
        try:
            # Open file dialog for tilt series
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Select Tilt Series File",
                "",
                "Tilt Series Files (*.mrc *.st *.mdoc);;MRC Files (*.mrc);;Stack Files (*.st);;MDOC Files (*.mdoc);;All Files (*)"
            )

            if file_path:
                # Update input path in parameters tab if available
                if hasattr(self, 'enhanced_parameters_tab') and hasattr(self.enhanced_parameters_tab, 'in_prefix'):
                    self.enhanced_parameters_tab.in_prefix.setText(file_path)

                # Log the action
                if hasattr(self, 'log_text'):
                    self.log_text.append(f"📥 Loaded tilt series: {os.path.basename(file_path)}")

                # Update status
                self.status_bar.showMessage(f"📥 Loaded: {os.path.basename(file_path)}")

                # Try to auto-detect and set output directory
                input_dir = os.path.dirname(file_path)
                output_dir = os.path.join(input_dir, "aretomo_output")

                if hasattr(self, 'enhanced_parameters_tab') and hasattr(self.enhanced_parameters_tab, 'out_dir'):
                    self.enhanced_parameters_tab.out_dir.setText(output_dir)

                logger.info(f"Tilt series loaded: {file_path}")

        except Exception as e:
            logger.error(f"Error loading tilt series: {e}")
            if hasattr(self, 'log_text'):
                self.log_text.append(f"❌ Error loading tilt series: {str(e)}")
            self.status_bar.showMessage("❌ Failed to load tilt series")

    def start_processing(self):
        """Start processing"""
        self.log_text.append("🚀 Starting AreTomo3 processing...")
        self.process_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.status_bar.showMessage("🔄 Processing...")

    def stop_processing(self):
        """Stop processing"""
        self.log_text.append("⏹️ Stopping processing...")
        self.process_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_bar.showMessage("⏹️ Processing stopped")

    def show_settings(self):
        """Show settings dialog"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QFormLayout, QSpinBox, QCheckBox, QPushButton, QLineEdit

        dialog = QDialog(self)
        dialog.setWindowTitle("⚙️ AreTomo3-GUI Settings")
        dialog.setModal(True)
        dialog.resize(500, 400)

        layout = QVBoxLayout(dialog)

        # Create tabs
        tab_widget = QTabWidget()

        # General settings tab
        general_tab = QWidget()
        general_layout = QFormLayout(general_tab)

        # Theme setting
        theme_combo = QComboBox()
        theme_combo.addItems(["Light", "Dark"])
        theme_combo.setCurrentText(self.theme_manager.get_current_theme().title())
        theme_combo.currentTextChanged.connect(lambda text: self.theme_manager.set_theme(text.lower()) or self.apply_theme())
        general_layout.addRow("Theme:", theme_combo)

        # Auto-save settings
        auto_save_check = QCheckBox("Auto-save project settings")
        auto_save_check.setChecked(True)
        general_layout.addRow("Auto-save:", auto_save_check)

        # Default directories
        default_input = QLineEdit()
        default_input.setPlaceholderText("Default input directory...")
        general_layout.addRow("Default Input Dir:", default_input)

        tab_widget.addTab(general_tab, "General")

        # Performance settings tab
        perf_tab = QWidget()
        perf_layout = QFormLayout(perf_tab)

        # Thread count
        thread_count = QSpinBox()
        thread_count.setRange(1, 16)
        thread_count.setValue(4)
        perf_layout.addRow("Processing Threads:", thread_count)

        # Memory limit
        memory_limit = QSpinBox()
        memory_limit.setRange(1, 64)
        memory_limit.setValue(8)
        memory_limit.setSuffix(" GB")
        perf_layout.addRow("Memory Limit:", memory_limit)

        tab_widget.addTab(perf_tab, "Performance")

        layout.addWidget(tab_widget)

        # Buttons
        button_layout = QHBoxLayout()
        ok_btn = QPushButton("OK")
        ok_btn.clicked.connect(dialog.accept)
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(dialog.reject)

        button_layout.addStretch()
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

        dialog.exec()

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About AreTomo3-GUI",
            "🔬 AreTomo3-GUI\n\n"
            "A modern interface for AreTomo3\n"
            "Built with PyQt6"
        )

    # Fallback methods for when comprehensive tabs can't be imported
    def create_simple_configuration_tab(self):
        """Fallback simple main tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Input/Output section
        io_group = QGroupBox("📁 Input/Output Configuration")
        io_layout = QFormLayout(io_group)

        self.input_dir = QLineEdit()
        self.input_dir.setPlaceholderText("Select input directory...")
        input_btn = QPushButton("📂 Browse")
        input_btn.clicked.connect(self.browse_input_dir)

        input_layout = QHBoxLayout()
        input_layout.addWidget(self.input_dir)
        input_layout.addWidget(input_btn)
        io_layout.addRow("Input Directory:", input_layout)

        # Output directory with configuration options
        output_layout = QHBoxLayout()
        self.output_dir = QLineEdit()
        self.output_dir.setPlaceholderText("Output directory (auto-generated)")

        # Browse button for manual output directory selection
        output_browse_btn = QPushButton("🔍 Browse")
        output_browse_btn.clicked.connect(self.browse_output_dir)

        # Auto-generate checkbox
        self.auto_generate_output = QCheckBox("Auto-generate inside input folder")
        self.auto_generate_output.setChecked(True)
        self.auto_generate_output.toggled.connect(self.on_auto_generate_output_toggled)

        output_layout.addWidget(self.output_dir)
        output_layout.addWidget(output_browse_btn)
        output_layout.addWidget(self.auto_generate_output)
        io_layout.addRow("Output Directory:", output_layout)

        # Directory structure configuration
        structure_group = QGroupBox("📁 Directory Structure Configuration")
        structure_layout = QFormLayout(structure_group)

        # Output folder name
        self.output_folder_name = QLineEdit("aretomo3_output")
        self.output_folder_name.setPlaceholderText("Name for output folder inside input directory")
        structure_layout.addRow("Output Folder Name:", self.output_folder_name)

        # Monitor/Watch directory configuration
        self.monitor_inside_input = QCheckBox("Create monitor directories inside input folder")
        self.monitor_inside_input.setChecked(True)
        self.monitor_inside_input.setToolTip("Creates 'monitor' and 'watch' directories inside input folder")
        structure_layout.addRow("Monitor Setup:", self.monitor_inside_input)

        # Directory preview
        self.directory_preview = QLabel("Directory structure will be shown here")
        self.directory_preview.setStyleSheet("color: #666; font-family: monospace; padding: 10px; background: #f5f5f5; border-radius: 4px;")
        self.directory_preview.setWordWrap(True)
        structure_layout.addRow("Preview:", self.directory_preview)

        layout.addWidget(structure_group)

        layout.addWidget(io_group)

        # AreTomo3 configuration
        config_group = QGroupBox("⚙️ AreTomo3 Configuration")
        config_layout = QFormLayout(config_group)

        self.aretomo_path = QLineEdit()
        self.aretomo_path.setPlaceholderText("Path to AreTomo3 executable...")
        aretomo_btn = QPushButton("🔍 Browse")
        aretomo_btn.clicked.connect(self.browse_aretomo_path)

        aretomo_layout = QHBoxLayout()
        aretomo_layout.addWidget(self.aretomo_path)
        aretomo_layout.addWidget(aretomo_btn)
        config_layout.addRow("AreTomo3 Path:", aretomo_layout)

        layout.addWidget(config_group)

        # Control buttons
        button_layout = QHBoxLayout()

        self.load_btn = QPushButton("📥 Load Tilt Series")
        self.load_btn.clicked.connect(self.load_tilt_series)
        button_layout.addWidget(self.load_btn)

        self.process_btn = QPushButton("🚀 Start Processing")
        self.process_btn.clicked.connect(self.start_processing)
        button_layout.addWidget(self.process_btn)

        self.stop_btn = QPushButton("⏹️ Stop")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)

        layout.addLayout(button_layout)
        layout.addStretch()

        self.tab_widget.addTab(tab, "Project Setup")

    def create_simple_aretomo3_parameters_tab(self):
        """Fallback simple parameters tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Microscope parameters
        microscope_group = QGroupBox("🔬 Microscope Parameters")
        microscope_layout = QFormLayout(microscope_group)

        self.pixel_size = QDoubleSpinBox()
        self.pixel_size.setRange(0.1, 10.0)
        self.pixel_size.setValue(1.91)
        self.pixel_size.setSuffix(" Å")
        microscope_layout.addRow("Pixel Size:", self.pixel_size)

        self.voltage = QSpinBox()
        self.voltage.setRange(80, 300)
        self.voltage.setValue(300)
        self.voltage.setSuffix(" kV")
        microscope_layout.addRow("Voltage:", self.voltage)

        self.cs = QDoubleSpinBox()
        self.cs.setRange(0.0, 10.0)
        self.cs.setValue(2.7)
        self.cs.setSuffix(" mm")
        microscope_layout.addRow("Cs:", self.cs)

        layout.addWidget(microscope_group)

        # Processing parameters
        processing_group = QGroupBox("⚙️ Processing Parameters")
        processing_layout = QFormLayout(processing_group)

        self.tilt_axis = QDoubleSpinBox()
        self.tilt_axis.setRange(-180, 180)
        self.tilt_axis.setValue(-95.75)
        self.tilt_axis.setSuffix("°")
        processing_layout.addRow("Tilt Axis:", self.tilt_axis)

        self.vol_z = QSpinBox()
        self.vol_z.setRange(100, 4096)
        self.vol_z.setValue(2048)
        processing_layout.addRow("Volume Z:", self.vol_z)

        layout.addWidget(processing_group)
        layout.addStretch()

        self.tab_widget.addTab(tab, "Reconstruction Parameters")

    def create_simple_batch_tab(self):
        """Fallback simple batch tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        label = QLabel("📦 Batch Processing\n\nComprehensive batch processing features will be available when all dependencies are installed.")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # Use theme-aware color
        color = "#6c757d" if self.theme_manager.get_current_theme() == "light" else "#888888"
        label.setStyleSheet(f"color: {color}; padding: 50px;")
        layout.addWidget(label)

        self.tab_widget.addTab(tab, "Batch Manager")

    def create_simple_live_tab(self):
        """Fallback simple live tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        label = QLabel("Live Monitor\n\nReal-time monitoring and processing features will be available when all dependencies are installed.")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: #888888; padding: 50px;")
        layout.addWidget(label)

        self.tab_widget.addTab(tab, "Live Monitor")

    def create_simple_analysis_tab(self):
        """Fallback simple analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        label = QLabel("📊 Enhanced Analysis\n\nAdvanced analysis and visualization features will be available when all dependencies are installed.")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: #888888; padding: 50px;")
        layout.addWidget(label)

        self.tab_widget.addTab(tab, "📊 Enhanced Analysis")

    def create_simple_unified_analysis_tab(self):
        """Fallback simple unified analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        label = QLabel("🔬 Analysis Workbench\n\nComprehensive analysis and monitoring features will be available when all dependencies are installed.\n\nThis tab includes:\n• Enhanced Analysis functionality\n• Real-time Analysis monitoring\n• Embedded CTF Viewer\n• Embedded Motion Viewer\n• Combined analysis workflows")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: #888888; padding: 50px; line-height: 1.6;")
        layout.addWidget(label)

        self.tab_widget.addTab(tab, "🔬 Analysis Workbench")

    def create_simple_napari_viewer_tab(self):
        """Fallback simple Napari viewer tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        label = QLabel("Volume Viewer\n\nNapari 3D volume visualization will be available when dependencies are installed.\n\nInstall with:\npip install napari[all]\npip install mrcfile")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: #888888; padding: 50px;")
        layout.addWidget(label)

        self.tab_widget.addTab(tab, "Volume Viewer")

    def create_simple_export_tab(self):
        """Fallback simple export tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        label = QLabel("💾 Export\n\nAdvanced export features will be available when all dependencies are installed.")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: #888888; padding: 50px;")
        layout.addWidget(label)

        self.tab_widget.addTab(tab, "Export Tools")

    def create_simple_monitor_tab(self):
        """Fallback simple monitor tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        label = QLabel("📊 System Monitor\n\nSystem monitoring features will be available when all dependencies are installed.")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: #888888; padding: 50px;")
        layout.addWidget(label)

        self.tab_widget.addTab(tab, "📊 System Monitor")

    # Main tab callback methods
    def on_browse_aretomo(self):
        """Browse for AreTomo3 executable."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select AreTomo3 Executable", "", "Executable Files (*)"
        )
        if file_path:
            self.aretomo_path.setText(file_path)
            logger.info(f"AreTomo3 path set to: {file_path}")

    def on_browse_input(self):
        """Browse for input directory."""
        directory = QFileDialog.getExistingDirectory(self, "Select Input Directory")
        if directory:
            self.input_dir.setText(directory)
            logger.info(f"Input directory set to: {directory}")

    def on_browse_output(self):
        """Browse for output directory."""
        directory = QFileDialog.getExistingDirectory(self, "Select Output Directory")
        if directory:
            self.output_dir.setText(directory)
            logger.info(f"Output directory set to: {directory}")

    def on_load_tilt_series(self):
        """Load tilt series data with file browser."""
        logger.info("Loading tilt series...")

        # Open file dialog for tilt series
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Tilt Series File",
            "",
            "Tilt Series Files (*.mrc *.st *.mdoc);;MRC Files (*.mrc);;Stack Files (*.st);;MDOC Files (*.mdoc);;All Files (*)"
        )

        if file_path:
            try:
                # Update input path if available
                if hasattr(self, 'in_prefix'):
                    self.in_prefix.setText(file_path)

                # Log the action
                logger.info(f"Tilt series loaded: {file_path}")

                # Update status
                if hasattr(self, 'status_bar'):
                    self.status_bar.showMessage(f"📥 Loaded: {os.path.basename(file_path)}")

                # Show success message
                QMessageBox.information(
                    self,
                    "Tilt Series Loaded",
                    f"Successfully loaded tilt series:\n{os.path.basename(file_path)}\n\nPath: {file_path}"
                )

            except Exception as e:
                logger.error(f"Error loading tilt series: {e}")
                QMessageBox.warning(
                    self,
                    "Load Error",
                    f"Failed to load tilt series:\n{str(e)}"
                )

    def on_process(self):
        """Start AreTomo3 processing with validation."""
        logger.info("Starting processing...")

        # Validate AreTomo3 path
        if not hasattr(self, 'aretomo_path') or not self.aretomo_path.text().strip():
            QMessageBox.warning(
                self,
                "Missing AreTomo3 Path",
                "Please set the AreTomo3 executable path in the Configuration tab first."
            )
            return

        # Validate input
        if hasattr(self, 'in_prefix') and not self.in_prefix.text().strip():
            QMessageBox.warning(
                self,
                "Missing Input",
                "Please specify an input file or directory in the AreTomo3 Parameters tab."
            )
            return

        try:
            # Validate AreTomo3 path first
            aretomo_path = ""
            if hasattr(self, 'aretomo_path'):
                aretomo_path = self.aretomo_path.text().strip()

            if not aretomo_path:
                QMessageBox.warning(
                    self,
                    "AreTomo3 Path Missing",
                    "Please set the AreTomo3 executable path in the Configuration tab first."
                )
                return

            # Get input and output paths
            input_prefix = ""
            output_dir = ""

            if hasattr(self, 'input_prefix'):
                input_prefix = self.input_prefix.text().strip()
            if hasattr(self, 'output_dir'):
                output_dir = self.output_dir.text().strip()

            # Validate required paths
            if not input_prefix:
                QMessageBox.warning(
                    self,
                    "Input Missing",
                    "Please set the input prefix (file or directory) in the Configuration tab first."
                )
                return

            if not output_dir:
                QMessageBox.warning(
                    self,
                    "Output Directory Missing",
                    "Please set the output directory in the Configuration tab first."
                )
                return

            # Generate command from parameters
            if hasattr(self, 'aretomo3_params_tab'):
                command = self.aretomo3_params_tab.generate_command()

                # Replace AreTomo3 with actual path
                command = command.replace("AreTomo3", f'"{aretomo_path}"', 1)

                # Replace placeholders with actual paths
                command = command.replace(
                    "# -InPrefix [will be set by Batch/Live processing]",
                    f'-InPrefix "{input_prefix}"'
                )
                command = command.replace(
                    "# -OutDir [will be set by Batch/Live processing]",
                    f'-OutDir "{output_dir}"'
                )

                # Show command preview
                reply = QMessageBox.question(
                    self,
                    "Start Processing",
                    f"Ready to start AreTomo3 processing.\n\nCommand:\n{command}\n\nProceed?",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    # Start actual processing
                    self._execute_aretomo3_command(command)
            else:
                QMessageBox.warning(
                    self,
                    "Parameters Not Available",
                    "AreTomo3 parameters tab not found. Please check the GUI initialization."
                )

        except Exception as e:
            logger.error(f"Error starting processing: {e}")
            QMessageBox.warning(
                self,
                "Processing Error",
                f"Failed to start processing:\n{str(e)}"
            )

    def on_stop(self):
        """Stop AreTomo3 processing."""
        logger.info("Stopping processing...")

        # Confirm stop action
        reply = QMessageBox.question(
            self,
            "Stop Processing",
            "Are you sure you want to stop the current processing?\n\nThis will terminate any running AreTomo3 processes.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # Stop processing (placeholder for actual implementation)
                logger.info("Stopping AreTomo3 processing")

                # Update UI state
                if hasattr(self, 'status_bar'):
                    self.status_bar.showMessage("⏹️ Processing stopped")

                QMessageBox.information(
                    self,
                    "Processing Stopped",
                    "AreTomo3 processing has been stopped."
                )

            except Exception as e:
                logger.error(f"Error stopping processing: {e}")
                QMessageBox.warning(
                    self,
                    "Stop Error",
                    f"Failed to stop processing:\n{str(e)}"
                )

    def _execute_aretomo3_command(self, command: str):
        """Execute AreTomo3 command in a separate process."""
        try:
            import subprocess
            import threading

            # Log the command
            logger.info(f"Executing AreTomo3 command: {command}")
            if hasattr(self, 'log_text'):
                self.log_text.append(f"🚀 Starting AreTomo3 processing...")
                self.log_text.append(f"📋 Command: {command}")

            # Update UI state
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage("🚀 AreTomo3 processing started...")

            # Create output directory if it doesn't exist
            if "-OutDir" in command:
                import re
                match = re.search(r'-OutDir\s+"([^"]+)"', command)
                if match:
                    output_dir = match.group(1)
                    import os
                    os.makedirs(output_dir, exist_ok=True)
                    logger.info(f"Created output directory: {output_dir}")

            def run_command():
                """Run the command in a separate thread."""
                try:
                    # Split command for subprocess
                    import shlex
                    cmd_args = shlex.split(command)

                    # Start the process
                    process = subprocess.Popen(
                        cmd_args,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        bufsize=1,
                        universal_newlines=True
                    )

                    # Store process for potential termination
                    self._current_process = process

                    # Read output in real-time
                    while True:
                        output = process.stdout.readline()
                        if output == '' and process.poll() is not None:
                            break
                        if output:
                            if hasattr(self, 'log_text'):
                                self.log_text.append(f"📄 {output.strip()}")

                    # Get final return code
                    return_code = process.poll()

                    # Handle completion
                    if return_code == 0:
                        if hasattr(self, 'log_text'):
                            self.log_text.append("✅ AreTomo3 processing completed successfully!")
                        if hasattr(self, 'status_bar'):
                            self.status_bar.showMessage("✅ Processing completed successfully")

                        # Show completion dialog
                        QMessageBox.information(
                            self,
                            "Processing Complete",
                            "AreTomo3 processing completed successfully!\n\nCheck the output directory for results."
                        )
                    else:
                        error_output = process.stderr.read()
                        if hasattr(self, 'log_text'):
                            self.log_text.append(f"❌ AreTomo3 processing failed with return code: {return_code}")
                            if error_output:
                                self.log_text.append(f"❌ Error: {error_output}")
                        if hasattr(self, 'status_bar'):
                            self.status_bar.showMessage("❌ Processing failed")

                        # Show error dialog
                        QMessageBox.warning(
                            self,
                            "Processing Failed",
                            f"AreTomo3 processing failed with return code: {return_code}\n\nCheck the logs for details."
                        )

                except Exception as e:
                    error_msg = f"Error executing command: {str(e)}"
                    logger.error(error_msg)
                    if hasattr(self, 'log_text'):
                        self.log_text.append(f"❌ {error_msg}")
                    if hasattr(self, 'status_bar'):
                        self.status_bar.showMessage("❌ Execution error")

                    # Show error dialog
                    QMessageBox.critical(
                        self,
                        "Execution Error",
                        f"Failed to execute AreTomo3:\n{str(e)}"
                    )
                finally:
                    # Clean up
                    self._current_process = None

            # Start command in separate thread
            command_thread = threading.Thread(target=run_command, daemon=True)
            command_thread.start()

        except Exception as e:
            error_msg = f"Error starting AreTomo3 execution: {str(e)}"
            logger.error(error_msg)
            if hasattr(self, 'log_text'):
                self.log_text.append(f"❌ {error_msg}")
            QMessageBox.critical(
                self,
                "Execution Error",
                error_msg
            )

    class TiltSeries:
        """Simple tilt series class for batch processing compatibility."""
        def __init__(self, name: str, file_path: str, directory: str, file_type: str = 'unknown'):
            self.position_name = name
            self.file_path = file_path
            self.directory = directory
            self.file_type = file_type
            self.files = [file_path]  # For compatibility with batch processing
            self.tilt_angles = []
            self.frame_count = 0
            self.pixel_size = None
            self.voltage = None
            self.total_dose = None

    def find_tilt_series(self, directory: str) -> Dict[str, 'TiltSeries']:
        """
        Find and parse tilt series in the given directory.

        Args:
            directory: Directory path to search for tilt series

        Returns:
            Dictionary with series names as keys and TiltSeries objects as values
        """
        logger.info(f"Searching for tilt series in: {directory}")

        try:
            import os
            from pathlib import Path
            import glob

            directory_path = Path(directory)
            if not directory_path.exists():
                logger.warning(f"Directory does not exist: {directory}")
                return {}

            series_dict = {}

            # Look for different tilt series file patterns (AreTomo3 supported formats)
            patterns = [
                "*.mdoc",      # MDOC files (SerialEM metadata)
                "*.eer",       # EER files (Electron Event Representation)
                "*.tif",       # TIFF files
                "*.tiff",      # TIFF files (alternative extension)
                "*.mrc",       # MRC stack files
                "*.st",        # Stack files
                "*.mrcs",      # MRC stack files
                "*_TS_*.mrc",  # Tilt series pattern
                "*_TS_*.st",   # Tilt series pattern
                "*[*.eer",     # EER files with bracket notation
                "*[*.tif",     # TIFF files with bracket notation
                "*[*.tiff",    # TIFF files with bracket notation
            ]

            found_files = []
            for pattern in patterns:
                # Search recursively
                files = list(directory_path.rglob(pattern))
                found_files.extend(files)

            logger.info(f"Found {len(found_files)} potential tilt series files")

            # Process MDOC files first (most informative)
            mdoc_files = [f for f in found_files if f.suffix.lower() == '.mdoc']

            for mdoc_file in mdoc_files:
                try:
                    series_info = self._parse_mdoc_file(mdoc_file)
                    if series_info:
                        # Create TiltSeries object
                        series = self.TiltSeries(
                            name=mdoc_file.stem,
                            file_path=str(mdoc_file),
                            directory=str(mdoc_file.parent),
                            file_type='mdoc'
                        )

                        # Copy parsed information
                        series.tilt_angles = series_info.get('tilt_angles', [])
                        series.frame_count = series_info.get('frame_count', 0)
                        series.pixel_size = series_info.get('pixel_size')
                        series.voltage = series_info.get('voltage')
                        series.files = series_info.get('image_files', [str(mdoc_file)])

                        series_dict[mdoc_file.stem] = series
                        logger.info(f"Parsed MDOC series: {mdoc_file.stem}")
                except Exception as e:
                    logger.error(f"Error parsing MDOC file {mdoc_file}: {e}")

            # Process other file types if no MDOC files found
            if not series_dict:
                # Process EER files
                eer_files = [f for f in found_files if f.suffix.lower() == '.eer']
                for eer_file in eer_files:
                    try:
                        series_info = self._parse_eer_file(eer_file)
                        if series_info:
                            series = self.TiltSeries(
                                name=eer_file.stem,
                                file_path=str(eer_file),
                                directory=str(eer_file.parent),
                                file_type='eer'
                            )
                            series.tilt_angles = series_info.get('tilt_angles', [])
                            series.frame_count = series_info.get('frame_count', 0)
                            series.pixel_size = series_info.get('pixel_size')
                            series.voltage = series_info.get('voltage')
                            series.files = [str(eer_file)]
                            series_dict[eer_file.stem] = series
                            logger.info(f"Parsed EER series: {eer_file.stem}")
                    except Exception as e:
                        logger.error(f"Error parsing EER file {eer_file}: {e}")

                # Process TIFF files
                tiff_files = [f for f in found_files if f.suffix.lower() in ['.tif', '.tiff']]
                for tiff_file in tiff_files:
                    try:
                        series_info = self._parse_tiff_file(tiff_file)
                        if series_info:
                            series = self.TiltSeries(
                                name=tiff_file.stem,
                                file_path=str(tiff_file),
                                directory=str(tiff_file.parent),
                                file_type='tiff'
                            )
                            series.tilt_angles = series_info.get('tilt_angles', [])
                            series.frame_count = series_info.get('frame_count', 0)
                            series.pixel_size = series_info.get('pixel_size')
                            series.voltage = series_info.get('voltage')
                            series.files = [str(tiff_file)]
                            series_dict[tiff_file.stem] = series
                            logger.info(f"Parsed TIFF series: {tiff_file.stem}")
                    except Exception as e:
                        logger.error(f"Error parsing TIFF file {tiff_file}: {e}")

                # Process MRC/ST files
                mrc_files = [f for f in found_files if f.suffix.lower() in ['.mrc', '.st', '.mrcs']]
                for mrc_file in mrc_files:
                    try:
                        series_info = self._parse_mrc_file(mrc_file)
                        if series_info:
                            series = self.TiltSeries(
                                name=mrc_file.stem,
                                file_path=str(mrc_file),
                                directory=str(mrc_file.parent),
                                file_type='mrc'
                            )
                            series.tilt_angles = series_info.get('tilt_angles', [])
                            series.frame_count = series_info.get('frame_count', 0)
                            series.pixel_size = series_info.get('pixel_size')
                            series.voltage = series_info.get('voltage')
                            series.files = [str(mrc_file)]
                            series_dict[mrc_file.stem] = series
                            logger.info(f"Parsed MRC series: {mrc_file.stem}")
                    except Exception as e:
                        logger.error(f"Error parsing MRC file {mrc_file}: {e}")

            logger.info(f"Found {len(series_dict)} tilt series in {directory}")
            return series_dict

        except Exception as e:
            logger.error(f"Error finding tilt series in {directory}: {e}")
            return {}

    def _parse_mdoc_file(self, mdoc_file: Path) -> Dict[str, Any]:
        """Parse MDOC file to extract tilt series information."""
        try:
            series_info = {
                'file_path': str(mdoc_file),
                'file_type': 'mdoc',
                'directory': str(mdoc_file.parent),
                'name': mdoc_file.stem,
                'tilt_angles': [],
                'image_files': [],
                'pixel_size': None,
                'voltage': None,
                'total_dose': None,
                'frame_count': 0
            }

            with open(mdoc_file, 'r') as f:
                lines = f.readlines()

            current_section = None
            for line in lines:
                line = line.strip()

                if line.startswith('[ZValue'):
                    # New image section
                    current_section = 'image'
                elif line.startswith('['):
                    current_section = 'header'
                elif '=' in line and current_section:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()

                    if current_section == 'image':
                        if key == 'TiltAngle':
                            try:
                                series_info['tilt_angles'].append(float(value))
                            except ValueError:
                                pass
                        elif key == 'SubFramePath':
                            series_info['image_files'].append(value)
                    elif current_section == 'header':
                        if key == 'PixelSpacing':
                            try:
                                series_info['pixel_size'] = float(value)
                            except ValueError:
                                pass
                        elif key == 'Voltage':
                            try:
                                series_info['voltage'] = float(value)
                            except ValueError:
                                pass

            series_info['frame_count'] = len(series_info['tilt_angles'])

            if series_info['frame_count'] > 0:
                logger.info(f"MDOC file {mdoc_file.name}: {series_info['frame_count']} frames, "
                           f"tilt range: {min(series_info['tilt_angles']):.1f}° to {max(series_info['tilt_angles']):.1f}°")
                return series_info
            else:
                logger.warning(f"No valid frames found in MDOC file: {mdoc_file}")
                return None

        except Exception as e:
            logger.error(f"Error parsing MDOC file {mdoc_file}: {e}")
            return None

    def _parse_mrc_file(self, mrc_file: Path) -> Dict[str, Any]:
        """Parse MRC file to extract basic tilt series information."""
        try:
            series_info = {
                'file_path': str(mrc_file),
                'file_type': 'mrc',
                'directory': str(mrc_file.parent),
                'name': mrc_file.stem,
                'tilt_angles': [],
                'image_files': [str(mrc_file)],
                'pixel_size': None,
                'voltage': None,
                'total_dose': None,
                'frame_count': 0
            }

            # Try to read MRC header for basic info
            try:
                import struct
                with open(mrc_file, 'rb') as f:
                    # Read MRC header (first 1024 bytes)
                    header = f.read(1024)
                    if len(header) >= 12:
                        # Extract dimensions (nx, ny, nz)
                        nx, ny, nz = struct.unpack('<3i', header[:12])
                        series_info['frame_count'] = nz

                        logger.info(f"MRC file {mrc_file.name}: {nz} frames, dimensions: {nx}x{ny}")

                        # Look for associated .tlt file for tilt angles
                        tlt_file = mrc_file.with_suffix('.tlt')
                        if tlt_file.exists():
                            with open(tlt_file, 'r') as tlt_f:
                                for line in tlt_f:
                                    try:
                                        angle = float(line.strip())
                                        series_info['tilt_angles'].append(angle)
                                    except ValueError:
                                        pass

                        if series_info['frame_count'] > 0:
                            return series_info

            except Exception as e:
                logger.warning(f"Could not read MRC header for {mrc_file}: {e}")
                # Still return basic info
                series_info['frame_count'] = 1  # Assume single frame if can't read header
                return series_info

        except Exception as e:
            logger.error(f"Error parsing MRC file {mrc_file}: {e}")
            return None

    def _parse_eer_file(self, eer_file: Path) -> Dict[str, Any]:
        """Parse EER file to extract basic information."""
        try:
            series_info = {
                'file_path': str(eer_file),
                'file_type': 'eer',
                'directory': str(eer_file.parent),
                'name': eer_file.stem,
                'tilt_angles': [],
                'image_files': [str(eer_file)],
                'pixel_size': None,
                'voltage': None,
                'total_dose': None,
                'frame_count': 1  # EER files are typically single frames
            }

            # Look for associated .tlt file for tilt angles
            tlt_file = eer_file.with_suffix('.tlt')
            if tlt_file.exists():
                with open(tlt_file, 'r') as tlt_f:
                    for line in tlt_f:
                        try:
                            angle = float(line.strip())
                            series_info['tilt_angles'].append(angle)
                        except ValueError:
                            pass
                series_info['frame_count'] = len(series_info['tilt_angles'])

            # Look for associated .mdoc file for metadata
            mdoc_file = eer_file.with_suffix('.mdoc')
            if mdoc_file.exists():
                try:
                    mdoc_info = self._parse_mdoc_file(mdoc_file)
                    if mdoc_info:
                        series_info.update(mdoc_info)
                except Exception as e:
                    logger.warning(f"Could not parse associated MDOC file {mdoc_file}: {e}")

            logger.info(f"EER file {eer_file.name}: {series_info['frame_count']} frames")
            return series_info

        except Exception as e:
            logger.error(f"Error parsing EER file {eer_file}: {e}")
            return None

    def _parse_tiff_file(self, tiff_file: Path) -> Dict[str, Any]:
        """Parse TIFF file to extract basic information."""
        try:
            series_info = {
                'file_path': str(tiff_file),
                'file_type': 'tiff',
                'directory': str(tiff_file.parent),
                'name': tiff_file.stem,
                'tilt_angles': [],
                'image_files': [str(tiff_file)],
                'pixel_size': None,
                'voltage': None,
                'total_dose': None,
                'frame_count': 1
            }

            # Try to read TIFF metadata
            try:
                from PIL import Image
                with Image.open(tiff_file) as img:
                    # Get number of frames in TIFF stack
                    frame_count = 1
                    try:
                        while True:
                            img.seek(frame_count)
                            frame_count += 1
                    except EOFError:
                        pass

                    series_info['frame_count'] = frame_count

                    # Try to get pixel size from TIFF tags
                    if hasattr(img, 'tag'):
                        # TIFF tag 282 = X Resolution, 283 = Y Resolution
                        x_res = img.tag.get(282)
                        if x_res:
                            try:
                                # Convert to pixel size in Angstroms (if resolution is in pixels/cm)
                                res_value = x_res[0][0] / x_res[0][1] if x_res[0][1] != 0 else x_res[0][0]
                                if res_value > 0:
                                    series_info['pixel_size'] = 1.0 / res_value * 10000000  # Convert to Angstroms
                            except:
                                pass

            except ImportError:
                logger.warning("PIL not available for TIFF parsing, using basic info")
            except Exception as e:
                logger.warning(f"Could not read TIFF metadata: {e}")

            # Look for associated .tlt file for tilt angles
            tlt_file = tiff_file.with_suffix('.tlt')
            if tlt_file.exists():
                with open(tlt_file, 'r') as tlt_f:
                    tilt_angles = []
                    for line in tlt_f:
                        try:
                            angle = float(line.strip())
                            tilt_angles.append(angle)
                        except ValueError:
                            pass
                    if tilt_angles:
                        series_info['tilt_angles'] = tilt_angles
                        series_info['frame_count'] = len(tilt_angles)

            # Look for associated .mdoc file for metadata
            mdoc_file = tiff_file.with_suffix('.mdoc')
            if mdoc_file.exists():
                try:
                    mdoc_info = self._parse_mdoc_file(mdoc_file)
                    if mdoc_info:
                        series_info.update(mdoc_info)
                except Exception as e:
                    logger.warning(f"Could not parse associated MDOC file {mdoc_file}: {e}")

            logger.info(f"TIFF file {tiff_file.name}: {series_info['frame_count']} frames")
            return series_info

        except Exception as e:
            logger.error(f"Error parsing TIFF file {tiff_file}: {e}")
            return None

    def update_ui_from_series(self, series_info):
        """Update UI elements with information from a tilt series."""
        try:
            # Update input path in parameters tab
            if hasattr(self, 'enhanced_parameters_tab') and hasattr(self.enhanced_parameters_tab, 'in_prefix'):
                self.enhanced_parameters_tab.in_prefix.setText(series_info['file_path'])

            # Update output directory
            if hasattr(self, 'enhanced_parameters_tab') and hasattr(self.enhanced_parameters_tab, 'out_dir'):
                output_dir = os.path.join(series_info['directory'], "aretomo_output")
                self.enhanced_parameters_tab.out_dir.setText(output_dir)

            # Update pixel size if available
            if (series_info.get('pixel_size') and
                hasattr(self, 'enhanced_parameters_tab') and
                hasattr(self.enhanced_parameters_tab, 'pix_size')):
                self.enhanced_parameters_tab.pix_size.setValue(series_info['pixel_size'])

            # Update voltage if available
            if (series_info.get('voltage') and
                hasattr(self, 'enhanced_parameters_tab') and
                hasattr(self.enhanced_parameters_tab, 'kv')):
                self.enhanced_parameters_tab.kv.setValue(series_info['voltage'])

            # Update status
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage(f"📥 Loaded: {series_info['name']} ({series_info['frame_count']} frames)")

            logger.info(f"Updated UI with series: {series_info['name']}")

        except Exception as e:
            logger.error(f"Error updating UI from series: {e}")

    def open_file(self):
        """Open project configuration file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open AreTomo3 Project",
            str(Path.home()),
            "AreTomo3 Projects (*.at3proj);;JSON Files (*.json);;All Files (*)"
        )
        if file_path:
            try:
                self.load_project_configuration(file_path)
                self.status_bar.showMessage(f"✅ Project loaded: {os.path.basename(file_path)}")
                logger.info(f"Project configuration loaded: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load project: {str(e)}")
                logger.error(f"Error loading project: {e}")

    def save_file(self):
        """Save project configuration file."""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save AreTomo3 Project",
            str(Path.home() / "aretomo3_project.at3proj"),
            "AreTomo3 Projects (*.at3proj);;JSON Files (*.json);;All Files (*)"
        )
        if file_path:
            try:
                self.save_project_configuration(file_path)
                self.status_bar.showMessage(f"✅ Project saved: {os.path.basename(file_path)}")
                logger.info(f"Project configuration saved: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save project: {str(e)}")
                logger.error(f"Error saving project: {e}")

    def save_project_configuration(self, file_path: str):
        """Save current project configuration to file."""
        import json
        from datetime import datetime

        config = {
            "aretomo3_project": {
                "version": "1.0",
                "created": datetime.now().isoformat(),
                "gui_settings": self.collect_gui_settings(),
                "processing_parameters": self.collect_processing_parameters(),
                "paths": self.collect_path_settings(),
                "recent_files": getattr(self, 'recent_files', []),
                "window_state": {
                    "geometry": self.saveGeometry().data().hex(),
                    "window_state": self.saveState().data().hex(),
                    "current_tab": self.tab_widget.currentIndex()
                }
            }
        }

        with open(file_path, 'w') as f:
            json.dump(config, f, indent=2)

        # Update recent files
        self.add_to_recent_files(file_path)

    def load_project_configuration(self, file_path: str):
        """Load project configuration from file."""
        import json

        with open(file_path, 'r') as f:
            config = json.load(f)

        project_config = config.get("aretomo3_project", {})

        # Restore GUI settings
        if "gui_settings" in project_config:
            self.restore_gui_settings(project_config["gui_settings"])

        # Restore processing parameters
        if "processing_parameters" in project_config:
            self.restore_processing_parameters(project_config["processing_parameters"])

        # Restore paths
        if "paths" in project_config:
            self.restore_path_settings(project_config["paths"])

        # Restore window state
        if "window_state" in project_config:
            self.restore_window_state(project_config["window_state"])

        # Update recent files
        self.add_to_recent_files(file_path)

        # Sync all tabs with loaded settings
        self.sync_all_tabs_with_settings()

    def on_export(self):
        """Export data."""
        logger.info("Exporting data...")
        QMessageBox.information(self, "Export", "Export functionality will be implemented here.")

    def collect_gui_settings(self):
        """Collect current GUI settings including directory configuration."""
        settings = {}

        # Collect basic settings from main window
        if hasattr(self, 'input_dir'):
            settings['input_directory'] = self.input_dir.text()
        if hasattr(self, 'output_dir'):
            settings['output_directory'] = self.output_dir.text()
        if hasattr(self, 'aretomo_path'):
            settings['aretomo3_path'] = self.aretomo_path.text()

        # Collect directory configuration settings
        if hasattr(self, 'auto_generate_output'):
            settings['auto_generate_output'] = self.auto_generate_output.isChecked()
        if hasattr(self, 'output_folder_name'):
            settings['output_folder_name'] = self.output_folder_name.text()
        if hasattr(self, 'monitor_inside_input'):
            settings['monitor_inside_input'] = self.monitor_inside_input.isChecked()

        # Collect theme settings
        settings['current_theme'] = getattr(self, 'current_theme', 'Light')

        return settings

    def collect_processing_parameters(self):
        """Collect current processing parameters."""
        parameters = {}

        # Try to get parameters from AreTomo3 Parameters tab
        if hasattr(self, 'parameters_tab'):
            try:
                parameters = self.parameters_tab.get_all_parameters()
            except:
                pass

        return parameters

    def collect_path_settings(self):
        """Collect current path settings."""
        paths = {}

        if hasattr(self, 'input_dir'):
            paths['input_directory'] = self.input_dir.text()
        if hasattr(self, 'output_dir'):
            paths['output_directory'] = self.output_dir.text()
        if hasattr(self, 'aretomo_path'):
            paths['aretomo3_executable'] = self.aretomo_path.text()

        return paths

    def restore_gui_settings(self, settings):
        """Restore GUI settings including directory configuration."""
        # Restore basic settings
        if 'input_directory' in settings and hasattr(self, 'input_dir'):
            self.input_dir.setText(settings['input_directory'])
        if 'output_directory' in settings and hasattr(self, 'output_dir'):
            self.output_dir.setText(settings['output_directory'])
        if 'aretomo3_path' in settings and hasattr(self, 'aretomo_path'):
            self.aretomo_path.setText(settings['aretomo3_path'])

        # Restore directory configuration settings
        if 'auto_generate_output' in settings and hasattr(self, 'auto_generate_output'):
            self.auto_generate_output.setChecked(settings['auto_generate_output'])
        if 'output_folder_name' in settings and hasattr(self, 'output_folder_name'):
            self.output_folder_name.setText(settings['output_folder_name'])
        if 'monitor_inside_input' in settings and hasattr(self, 'monitor_inside_input'):
            self.monitor_inside_input.setChecked(settings['monitor_inside_input'])

        # Restore theme
        if 'current_theme' in settings:
            self.switch_theme(settings['current_theme'])

        # Update directory preview after restoring settings
        self.update_directory_preview()

    def restore_processing_parameters(self, parameters):
        """Restore processing parameters."""
        if hasattr(self, 'parameters_tab') and parameters:
            try:
                self.parameters_tab.set_all_parameters(parameters)
            except:
                pass

    def restore_path_settings(self, paths):
        """Restore path settings."""
        if 'input_directory' in paths and hasattr(self, 'input_dir'):
            self.input_dir.setText(paths['input_directory'])
        if 'output_directory' in paths and hasattr(self, 'output_dir'):
            self.output_dir.setText(paths['output_directory'])
        if 'aretomo3_executable' in paths and hasattr(self, 'aretomo_path'):
            self.aretomo_path.setText(paths['aretomo3_executable'])

    def restore_window_state(self, window_state):
        """Restore window state."""
        try:
            if 'geometry' in window_state:
                geometry = bytes.fromhex(window_state['geometry'])
                self.restoreGeometry(geometry)
            if 'window_state' in window_state:
                state = bytes.fromhex(window_state['window_state'])
                self.restoreState(state)
            if 'current_tab' in window_state:
                self.tab_widget.setCurrentIndex(window_state['current_tab'])
        except:
            pass

    def add_to_recent_files(self, file_path):
        """Add file to recent files list."""
        if not hasattr(self, 'recent_files'):
            self.recent_files = []

        # Remove if already exists
        if file_path in self.recent_files:
            self.recent_files.remove(file_path)

        # Add to beginning
        self.recent_files.insert(0, file_path)

        # Keep only last 10
        self.recent_files = self.recent_files[:10]

        # Update menu if available
        if hasattr(self, 'menu_manager'):
            self.menu_manager.update_recent_files(self.recent_files)

    def sync_all_tabs_with_settings(self):
        """Synchronize all tabs with loaded settings."""
        # Sync processing status across all tabs
        self.sync_processing_status()

        # Update web server status
        self.sync_web_server_status("🔴 Web Server: Stopped", False)

        logger.info("All tabs synchronized with loaded settings")

    def sync_processing_status(self, status="idle", message="Ready", progress=0):
        """Synchronize processing status across all tabs."""
        try:
            # Update all tabs with processing status
            tabs_to_sync = [
                'project_setup_tab',
                'batch_tab_manager',
                'live_processing_tab',
                'unified_analysis_tab',
                'parameters_tab',
                'enhanced_monitor_tab'
            ]

            for tab_name in tabs_to_sync:
                if hasattr(self, tab_name):
                    tab = getattr(self, tab_name)

                    # Update status if tab has status update method
                    if hasattr(tab, 'update_processing_status'):
                        tab.update_processing_status(status, message, progress)
                    elif hasattr(tab, 'update_status'):
                        tab.update_status(message)

                    # Update progress if tab has progress bar
                    if hasattr(tab, 'update_progress'):
                        tab.update_progress(progress)

            # Update main window status bar
            status_icons = {
                'idle': '🟢',
                'processing': '🔄',
                'completed': '✅',
                'error': '❌',
                'stopped': '⏹️'
            }

            icon = status_icons.get(status, '🟡')
            self.status_bar.showMessage(f"{icon} {message}")

            logger.debug(f"Processing status synchronized: {status} - {message}")

        except Exception as e:
            logger.error(f"Error synchronizing processing status: {e}")

    def start_processing_with_sync(self):
        """Start processing with full tab synchronization."""
        self.sync_processing_status("processing", "AreTomo3 processing started...", 0)

        # Start actual processing
        self.start_processing()

        # Update all tabs
        self.sync_all_tabs_with_processing_state(True)

    def stop_processing_with_sync(self):
        """Stop processing with full tab synchronization."""
        self.sync_processing_status("stopped", "Processing stopped by user", 0)

        # Stop actual processing
        self.stop_processing()

        # Update all tabs
        self.sync_all_tabs_with_processing_state(False)

    def sync_all_tabs_with_processing_state(self, is_processing):
        """Sync all tabs with current processing state."""
        try:
            # Update batch tab
            if hasattr(self, 'batch_tab_manager'):
                batch_widget = self.batch_tab_manager.get_batch_widget()
                if batch_widget and hasattr(batch_widget, 'set_processing_state'):
                    batch_widget.set_processing_state(is_processing)

            # Update live processing tab
            if hasattr(self, 'live_processing_tab'):
                if hasattr(self.live_processing_tab, 'set_processing_state'):
                    self.live_processing_tab.set_processing_state(is_processing)

            # Update analysis tab
            if hasattr(self, 'unified_analysis_tab'):
                if hasattr(self.unified_analysis_tab, 'set_processing_state'):
                    self.unified_analysis_tab.set_processing_state(is_processing)

            # Update monitor tab
            if hasattr(self, 'enhanced_monitor_tab'):
                if hasattr(self.enhanced_monitor_tab, 'set_processing_state'):
                    self.enhanced_monitor_tab.set_processing_state(is_processing)

            logger.info(f"All tabs synchronized with processing state: {is_processing}")

        except Exception as e:
            logger.error(f"Error syncing tabs with processing state: {e}")

    def on_start_queue_processing(self, queue_items):
        """Start queue processing with actual AreTomo3 execution."""
        logger.info(f"Starting queue processing with {len(queue_items)} items...")

        try:
            # Validate AreTomo3 path first
            aretomo_path = ""
            if hasattr(self, 'aretomo_path'):
                aretomo_path = self.aretomo_path.text().strip()

            if not aretomo_path:
                QMessageBox.warning(
                    self,
                    "AreTomo3 Path Missing",
                    "Please set the AreTomo3 executable path in the Configuration tab first."
                )
                return

            # Process each item in the queue
            for i, item in enumerate(queue_items):
                logger.info(f"Processing item {i+1}/{len(queue_items)}: {item.get('position', 'Unknown')}")

                # Get paths from the batch item
                input_prefix = item.get('input_dir', '')
                output_dir = item.get('output_dir', '')
                position = item.get('position', f'item_{i+1}')

                if not input_prefix or not output_dir:
                    logger.error(f"Missing paths for {position}: input={input_prefix}, output={output_dir}")
                    continue

                # Auto-extract parameters from .mdoc file if available
                self._auto_extract_mdoc_parameters(input_prefix, position)

                # Auto-detect gain file in input directory
                self._auto_detect_gain_file(input_prefix, position)

                # Generate command from parameters
                if hasattr(self, 'aretomo3_params_tab'):
                    command = self.aretomo3_params_tab.generate_command()

                    # Replace AreTomo3 with actual path
                    command = command.replace("AreTomo3", f'"{aretomo_path}"', 1)

                    # Replace placeholders with actual paths
                    # Fix InPrefix to be the correct format for AreTomo3
                    correct_inprefix = self._get_correct_inprefix(input_prefix, position)
                    command = command.replace(
                        "# -InPrefix [will be set by Batch/Live processing]",
                        f'-InPrefix "{correct_inprefix}"'
                    )
                    command = command.replace(
                        "# -OutDir [will be set by Batch/Live processing]",
                        f'-OutDir "{output_dir}"'
                    )

                    logger.info(f"Generated command for {position}: {command}")

                    # Execute the command
                    self._execute_aretomo3_command_for_batch(command, position, i+1, len(queue_items))

                else:
                    logger.error("AreTomo3 parameters tab not found")
                    QMessageBox.warning(
                        self,
                        "Parameters Not Available",
                        "AreTomo3 parameters tab not found. Please check the GUI initialization."
                    )
                    return

        except Exception as e:
            logger.error(f"Error in queue processing: {e}")
            QMessageBox.critical(
                self,
                "Queue Processing Error",
                f"Failed to start queue processing:\n{str(e)}"
            )

    def _execute_aretomo3_command_for_batch(self, command: str, position: str, current_item: int, total_items: int):
        """Execute AreTomo3 command for batch processing."""
        try:
            import subprocess
            import threading

            # Log the command
            logger.info(f"[Batch {current_item}/{total_items}] Executing AreTomo3 for {position}: {command}")

            # Update log widget if available
            log_widget = None
            if hasattr(self, 'log_tab_manager') and hasattr(self.log_tab_manager, 'log_viewer'):
                log_widget = self.log_tab_manager.log_viewer
            elif hasattr(self, 'log_text'):
                log_widget = self.log_text

            if log_widget and hasattr(log_widget, 'append'):
                log_widget.append(f"🚀 [{current_item}/{total_items}] Processing {position}...")
                log_widget.append(f"📋 Command: {command}")

            # Create output directory if it doesn't exist
            if "-OutDir" in command:
                import re
                match = re.search(r'-OutDir\s+"([^"]+)"', command)
                if match:
                    output_dir = match.group(1)
                    import os
                    os.makedirs(output_dir, exist_ok=True)
                    logger.info(f"Created output directory: {output_dir}")

            def run_batch_command():
                """Run the batch command in a separate thread."""
                try:
                    # Split command for subprocess
                    import shlex
                    cmd_args = shlex.split(command)

                    # Start the process
                    process = subprocess.Popen(
                        cmd_args,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        bufsize=1,
                        universal_newlines=True
                    )

                    # Read output in real-time
                    while True:
                        output = process.stdout.readline()
                        if output == '' and process.poll() is not None:
                            break
                        if output:
                            logger.info(f"[{position}] {output.strip()}")
                            if log_widget and hasattr(log_widget, 'append'):
                                log_widget.append(f"📄 [{position}] {output.strip()}")

                    # Get final return code
                    return_code = process.poll()

                    # Handle completion
                    if return_code == 0:
                        logger.info(f"[{position}] AreTomo3 processing completed successfully!")
                        if log_widget and hasattr(log_widget, 'append'):
                            log_widget.append(f"✅ [{position}] Processing completed successfully!")

                        # Update batch widget status if available
                        if hasattr(self, 'batch_tab_manager') and hasattr(self.batch_tab_manager, 'batch_widget'):
                            batch_widget = self.batch_tab_manager.batch_widget
                            if hasattr(batch_widget, 'update_item_status'):
                                batch_widget.update_item_status(position, "Completed")
                    else:
                        error_output = process.stderr.read()
                        logger.error(f"[{position}] AreTomo3 processing failed with return code: {return_code}")
                        if log_widget and hasattr(log_widget, 'append'):
                            log_widget.append(f"❌ [{position}] Processing failed with return code: {return_code}")
                            if error_output:
                                log_widget.append(f"❌ [{position}] Error: {error_output}")

                        # Update batch widget status if available
                        if hasattr(self, 'batch_tab_manager') and hasattr(self.batch_tab_manager, 'batch_widget'):
                            batch_widget = self.batch_tab_manager.batch_widget
                            if hasattr(batch_widget, 'update_item_status'):
                                batch_widget.update_item_status(position, "Failed")

                except Exception as e:
                    error_msg = f"[{position}] Error executing command: {str(e)}"
                    logger.error(error_msg)
                    if log_widget and hasattr(log_widget, 'append'):
                        log_widget.append(f"❌ {error_msg}")

                    # Update batch widget status if available
                    if hasattr(self, 'batch_tab_manager') and hasattr(self.batch_tab_manager, 'batch_widget'):
                        batch_widget = self.batch_tab_manager.batch_widget
                        if hasattr(batch_widget, 'update_item_status'):
                            batch_widget.update_item_status(position, "Error")

            # Start monitoring for this output directory
            if self.realtime_monitor and self.auto_plot_enabled:
                try:
                    self.realtime_monitor.add_directory(output_dir)
                    logger.info(f"Added {output_dir} to real-time monitoring")
                except Exception as e:
                    logger.warning(f"Could not add monitoring for {output_dir}: {e}")

            # Start command in separate thread
            command_thread = threading.Thread(target=run_batch_command, daemon=True)
            command_thread.start()

        except Exception as e:
            error_msg = f"Error starting AreTomo3 execution for {position}: {str(e)}"
            logger.error(error_msg)
            if log_widget and hasattr(log_widget, 'append'):
                log_widget.append(f"❌ {error_msg}")

    def _auto_extract_mdoc_parameters(self, input_prefix: str, position: str):
        """Auto-extract parameters from .mdoc file using proper mdocfile library."""
        try:
            import os
            from pathlib import Path
            import mdocfile

            # Find .mdoc file in the input directory
            input_path = Path(input_prefix)
            mdoc_files = []

            # Look for .mdoc files
            if input_path.is_dir():
                mdoc_files = list(input_path.glob("*.mdoc"))
            elif input_path.is_file() and input_path.suffix.lower() == '.mdoc':
                mdoc_files = [input_path]
            else:
                # Look for .mdoc files with the position name
                parent_dir = input_path.parent if input_path.is_file() else input_path
                mdoc_files = list(parent_dir.glob(f"{position}*.mdoc"))
                if not mdoc_files:
                    mdoc_files = list(parent_dir.glob("*.mdoc"))

            if not mdoc_files:
                logger.warning(f"No .mdoc files found for {position} in {input_prefix}")
                return

            # Use the first .mdoc file found
            mdoc_file = mdoc_files[0]
            logger.info(f"Auto-extracting parameters from {mdoc_file} for {position}")

            # Parse the .mdoc file using proper mdocfile library
            mdoc_df = mdocfile.read(str(mdoc_file))

            if mdoc_df is None or len(mdoc_df) == 0:
                logger.warning(f"Failed to parse {mdoc_file} or no data found")
                return

            # Extract key parameters from the mdoc DataFrame
            extracted_params = {}

            # Get first row as reference for global parameters
            first_row = mdoc_df.iloc[0]

            # Extract pixel size (convert from nm to Angstrom if needed)
            if 'PixelSpacing' in mdoc_df.columns and not mdoc_df['PixelSpacing'].isna().iloc[0]:
                pixel_size = float(first_row['PixelSpacing'])
                # Convert nm to Angstrom if value is small (likely in nm)
                if pixel_size < 10:  # Likely in nm
                    pixel_size *= 10
                extracted_params['pix_size'] = pixel_size
                logger.info(f"Extracted PixelSpacing={pixel_size} Å")

            # Extract voltage
            if 'Voltage' in mdoc_df.columns and not mdoc_df['Voltage'].isna().iloc[0]:
                extracted_params['kv'] = int(float(first_row['Voltage']))
                logger.info(f"Extracted Voltage={extracted_params['kv']} kV")

            # Extract spherical aberration (might be in file header, not DataFrame)
            # Try to extract from file content directly
            with open(mdoc_file, 'r') as f:
                content = f.read()
                import re

                # Look for TiltAxisAngle in header
                tilt_axis_match = re.search(r'TiltAxisAngle\s*=\s*([-\d.]+)', content)
                if tilt_axis_match:
                    extracted_params['tilt_axis'] = float(tilt_axis_match.group(1))
                    logger.info(f"Extracted TiltAxisAngle={extracted_params['tilt_axis']}°")

                # Look for SphericalAberration
                cs_match = re.search(r'SphericalAberration\s*=\s*([-\d.]+)', content)
                if cs_match:
                    extracted_params['cs'] = float(cs_match.group(1))
                    logger.info(f"Extracted SphericalAberration={extracted_params['cs']} mm")

            # Calculate total dose from all sections
            if 'ExposureDose' in mdoc_df.columns:
                total_dose = mdoc_df['ExposureDose'].sum()
                if total_dose > 0:
                    extracted_params['fm_dose'] = total_dose
                    logger.info(f"Calculated total dose={total_dose} e⁻/Å²")

            # Extract frame dose rate if available
            if 'FrameDoseRate' in mdoc_df.columns and not mdoc_df['FrameDoseRate'].isna().iloc[0]:
                if 'fm_dose' not in extracted_params:  # Only if total dose not calculated
                    extracted_params['fm_dose'] = float(first_row['FrameDoseRate'])
                    logger.info(f"Extracted FrameDoseRate={extracted_params['fm_dose']} e⁻/Å²/frame")

            # Update the parameters tab if we have extracted parameters
            if extracted_params and hasattr(self, 'aretomo3_params_tab'):
                self.aretomo3_params_tab.set_parameters(extracted_params)
                logger.info(f"Auto-updated parameters for {position}: {extracted_params}")

                # Log to GUI
                log_widget = None
                if hasattr(self, 'log_tab_manager') and hasattr(self.log_tab_manager, 'log_viewer'):
                    log_widget = self.log_tab_manager.log_viewer
                elif hasattr(self, 'log_text'):
                    log_widget = self.log_text

                if log_widget and hasattr(log_widget, 'append'):
                    log_widget.append(f"📋 [{position}] Auto-extracted parameters from {mdoc_file.name}")
                    for key, value in extracted_params.items():
                        log_widget.append(f"   • {key}: {value}")
            else:
                logger.warning(f"No parameters extracted from {mdoc_file} for {position}")

        except Exception as e:
            logger.error(f"Error auto-extracting parameters for {position}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    def _auto_detect_gain_file(self, input_prefix: str, position: str):
        """Auto-detect gain file in input directory and set it in parameters."""
        try:
            from pathlib import Path

            input_path = Path(input_prefix)

            # Look for gain files in the input directory
            if input_path.is_dir():
                search_dir = input_path
            elif input_path.is_file():
                search_dir = input_path.parent
            else:
                search_dir = Path(input_prefix).parent

            # Common gain file extensions and patterns
            gain_patterns = [
                "*.gain",
                "*.mrc",
                "*gain*.mrc",
                "*gainref*.gain",
                "*gainref*.mrc",
                "gainref*"
            ]

            gain_files = []
            for pattern in gain_patterns:
                gain_files.extend(search_dir.glob(pattern))

            # Filter to likely gain files (avoid other .mrc files)
            likely_gain_files = []
            for gain_file in gain_files:
                filename_lower = gain_file.name.lower()
                if any(keyword in filename_lower for keyword in ['gain', 'gainref']):
                    likely_gain_files.append(gain_file)
                elif gain_file.suffix.lower() == '.gain':
                    likely_gain_files.append(gain_file)

            if likely_gain_files:
                # Use the first gain file found
                gain_file = likely_gain_files[0]
                gain_filename = gain_file.name  # Just the filename, not full path

                logger.info(f"Auto-detected gain file: {gain_filename} for {position}")

                # Set the gain file in the parameters tab
                if hasattr(self, 'aretomo3_params_tab') and hasattr(self.aretomo3_params_tab, 'gain_file'):
                    self.aretomo3_params_tab.gain_file.setText(gain_filename)
                    logger.info(f"Set gain file parameter to: {gain_filename}")

                    # Log to GUI
                    log_widget = None
                    if hasattr(self, 'log_tab_manager') and hasattr(self.log_tab_manager, 'log_viewer'):
                        log_widget = self.log_tab_manager.log_viewer
                    elif hasattr(self, 'log_text'):
                        log_widget = self.log_text

                    if log_widget and hasattr(log_widget, 'append'):
                        log_widget.append(f"🎯 [{position}] Auto-detected gain file: {gain_filename}")
                else:
                    logger.warning("Could not set gain file - parameters tab not available")
            else:
                logger.warning(f"No gain files found in {search_dir} for {position}")

        except Exception as e:
            logger.error(f"Error auto-detecting gain file for {position}: {e}")

    def _get_correct_inprefix(self, input_prefix: str, position: str) -> str:
        """Get the correct InPrefix format for AreTomo3 - MUST match working bash script format."""
        try:
            from pathlib import Path

            input_path = Path(input_prefix)

            # CRITICAL: AreTomo3 expects directory with trailing slash for batch processing
            # This matches the working bash script format: "/path/to/directory/"
            if input_path.is_dir():
                # Use directory with trailing slash - this is the key difference!
                inprefix = str(input_path) + "/"
                logger.info(f"InPrefix (directory format): {inprefix}")
                return inprefix

            # If input_prefix is a file, use its parent directory
            elif input_path.is_file():
                parent_dir = input_path.parent
                inprefix = str(parent_dir) + "/"
                logger.info(f"InPrefix from file parent: {inprefix}")
                return inprefix

            # Fallback: ensure trailing slash
            if not input_prefix.endswith('/'):
                input_prefix += '/'
            logger.warning(f"Using input_prefix with trailing slash: {input_prefix}")
            return input_prefix

        except Exception as e:
            logger.error(f"Error getting correct InPrefix: {e}")
            # Ensure trailing slash even on error
            if not input_prefix.endswith('/'):
                input_prefix += '/'
            return input_prefix

    def _preview_command(self):
        """Preview the AreTomo3 command that would be executed."""
        logger.info("Previewing command...")

        # Build a sample command preview
        command_parts = ["AreTomo3"]

        # Add input/output if available
        if hasattr(self, 'input_dir') and self.input_dir.text():
            command_parts.append(f"-InMrc {self.input_dir.text()}")
        if hasattr(self, 'output_dir') and self.output_dir.text():
            command_parts.append(f"-OutMrc {self.output_dir.text()}")

        # Add GPU if available
        if hasattr(self, 'gpu_index'):
            command_parts.append(f"-Gpu {self.gpu_index.value()}")

        # Add common parameters
        command_parts.extend([
            "-VolZ 2048",
            "-OutBin 4",
            "-TiltAxis -95.75",
            "-AlignZ 800"
        ])

        command = " ".join(command_parts)

        # Update command preview if it exists
        if hasattr(self, 'command_preview'):
            self.command_preview.setText(command)

        # Show in a dialog
        QMessageBox.information(
            self,
            "Command Preview",
            f"AreTomo3 command that would be executed:\n\n{command}"
        )


# For compatibility
AreTomoGUI = RichAreTomoGUI
