#!/usr/bin/env python3
"""
Real-time Multi-Series Analysis Tab for AreTomo3 GUI.
Provides real-time monitoring and comparative analysis of multiple tilt series.
"""

import json
import logging
import os
import threading
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

import numpy as np
from PyQt6.QtCore import QFileSystemWatcher, Qt, QThread, QTimer, pyqtSignal, pyqtSlot
from PyQt6.QtGui import QColor, QFont, QIcon, QPixmap
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QFileDialog,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QHeaderView,
    QLabel,
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QMessageBox,
    QProgressBar,
    QPushButton,
    QScrollArea,
    <PERSON><PERSON>p<PERSON>ter,
    QTableWidget,
    QTableWidgetItem,
    QTabWidget,
    QT<PERSON>tE<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Q<PERSON>ree<PERSON>idgetI<PERSON>,
    Q<PERSON>oxLayout,
    QWidget,
)

try:
    import matplotlib.colors as mcolors
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

from ...utils.aretomo3_parser import AreTomo3ResultsParser

logger = logging.getLogger(__name__)


class RealTimeAnalysisTab(QWidget):
    """Real-time multi-series analysis tab with comparative plotting."""

    # Signals
    series_detected = pyqtSignal(str, str)  # series_name, status
    plots_updated = pyqtSignal(dict)  # plot_data
    quality_updated = pyqtSignal(dict)  # quality_metrics
    pdf_generated = pyqtSignal(str, str)  # series_name, pdf_path

    def __init__(self, parent=None):
        """Initialize the real-time analysis tab."""
        super().__init__(parent)
        self.main_window = parent

        # Data storage
        self.monitored_directories: Set[str] = set()
        self.series_data: Dict[str, Dict] = {}
        self.series_colors: Dict[str, str] = {}
        self.quality_metrics: Dict[str, Dict] = {}
        self.generated_plots: Dict[str, str] = {}

        # Real-time monitoring
        self.file_watcher = QFileSystemWatcher()
        self.file_watcher.directoryChanged.connect(self.on_directory_changed)

        # Update timer for real-time refresh (reduced frequency to prevent lag)
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_real_time_data)
        # Don't start automatically to prevent GUI freezing
        # Update every 30 seconds when enabled
        self.update_timer.setInterval(30000)

        # Processing completion timer (only update when processing finishes)
        self.processing_timer = QTimer()
        self.processing_timer.setSingleShot(True)
        self.processing_timer.timeout.connect(self.update_plots)

        # Color palette for series
        self.color_palette = [
            "#1f77b4",
            "#ff7f0e",
            "#2ca02c",
            "#d62728",
            "#9467bd",
            "#8c564b",
            "#e377c2",
            "#7f7f7f",
            "#bcbd22",
            "#17becf",
            "#aec7e8",
            "#ffbb78",
            "#98df8a",
            "#ff9896",
            "#c5b0d5",
        ]
        self.color_index = 0

        # Live processing mode support
        self.live_processing_mode = False
        self.monitoring_directory = None

        # Active series tracking (only show one series at a time, not
        # overlapped)
        self.active_series = None
        self.latest_series = None

        logger.info("Initializing Real-time Analysis Tab")
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Set up the user interface."""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)

        # Create main horizontal splitter - reorganized layout
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel - Plot sub-tabs (75% - increased for better plot
        # visibility)
        self.create_plot_panel(main_splitter)

        # Right panel - Tilt series selector (25% - optimized for selection)
        self.create_series_panel(main_splitter)

        # Set splitter proportions for optimal layout
        main_splitter.setStretchFactor(0, 75)  # Plots get more space
        main_splitter.setStretchFactor(1, 25)  # Series selector compact

        main_layout.addWidget(main_splitter)

    def create_plot_panel(self, parent):
        """Create the plot panel with sub-tabs."""
        plot_widget = QWidget()
        plot_layout = QVBoxLayout(plot_widget)

        # Enhanced plot controls with tilt angle x-axis
        controls_layout = QHBoxLayout()

        # Real-time toggle
        self.realtime_toggle = QCheckBox("🔄 Real-time Updates")
        self.realtime_toggle.setChecked(True)
        self.realtime_toggle.toggled.connect(self.toggle_realtime_updates)
        controls_layout.addWidget(self.realtime_toggle)

        # Auto-generate plots
        self.auto_plots_toggle = QCheckBox("📊 Auto-generate Plots")
        self.auto_plots_toggle.setChecked(True)
        controls_layout.addWidget(self.auto_plots_toggle)

        # X-axis selection for enhanced real-time analysis
        self.x_axis_combo = QComboBox()
        self.x_axis_combo.addItems(["Tilt Angle (°)", "Frame Number", "Time"])
        self.x_axis_combo.setCurrentText("Tilt Angle (°)")
        self.x_axis_combo.currentTextChanged.connect(self.update_plots)
        controls_layout.addWidget(QLabel("X-axis:"))
        controls_layout.addWidget(self.x_axis_combo)

        # Resolution units for enhanced display
        self.resolution_units = QComboBox()
        self.resolution_units.addItems(["Angstrom (Å)", "1/Å", "Pixels"])
        self.resolution_units.setCurrentText("Angstrom (Å)")
        self.resolution_units.currentTextChanged.connect(self.update_plots)
        controls_layout.addWidget(QLabel("Resolution:"))
        controls_layout.addWidget(self.resolution_units)

        # Max resolution limit (20Å as requested)
        self.max_resolution = QComboBox()
        self.max_resolution.addItems(["10Å", "15Å", "20Å", "30Å", "50Å"])
        self.max_resolution.setCurrentText("20Å")
        self.max_resolution.currentTextChanged.connect(self.update_plots)
        controls_layout.addWidget(QLabel("Max Res:"))
        controls_layout.addWidget(self.max_resolution)

        controls_layout.addStretch()

        # Export buttons
        self.export_plots_btn = QPushButton("💾 Export Plots")
        self.export_plots_btn.clicked.connect(self.export_all_plots)
        controls_layout.addWidget(self.export_plots_btn)

        self.generate_reports_btn = QPushButton("📄 Generate Reports")
        self.generate_reports_btn.clicked.connect(self.generate_pdf_reports)
        controls_layout.addWidget(self.generate_reports_btn)

        plot_layout.addLayout(controls_layout)

        # Create plot sub-tabs
        self.plot_tabs = QTabWidget()

        # Motion Analysis Tab
        self.create_motion_tab()

        # CTF Analysis Tab
        self.create_ctf_tab()

        # Alignment Analysis Tab
        self.create_alignment_tab()

        # Quality Dashboard Tab
        self.create_quality_tab()

        # Note: Comparative View Tab removed as per user request

        plot_layout.addWidget(self.plot_tabs)
        parent.addWidget(plot_widget)

    def create_motion_tab(self):
        """Create motion analysis tab."""
        motion_widget = QWidget()
        motion_layout = QVBoxLayout(motion_widget)

        # Add advanced visualizer controls
        motion_controls = QHBoxLayout()
        self.open_motion_viewer_btn_live = QPushButton(">> Open Advanced Motion Viewer")
        self.open_motion_viewer_btn_live.clicked.connect(self.open_motion_viewer_live)
        motion_controls.addWidget(self.open_motion_viewer_btn_live)
        motion_controls.addStretch()
        motion_layout.addLayout(motion_controls)

        if MATPLOTLIB_AVAILABLE:
            self.motion_figure = Figure(figsize=(12, 8))
            self.motion_figure.subplots_adjust(
                left=0.08, right=0.95, top=0.92, bottom=0.12, hspace=0.4, wspace=0.3
            )
            self.motion_canvas = FigureCanvas(self.motion_figure)
            motion_layout.addWidget(self.motion_canvas)
        else:
            motion_layout.addWidget(QLabel("Matplotlib not available"))

        self.plot_tabs.addTab(motion_widget, "📈 Motion Analysis")

    def create_ctf_tab(self):
        """Create CTF analysis tab."""
        ctf_widget = QWidget()
        ctf_layout = QVBoxLayout(ctf_widget)

        # Add advanced visualizer controls
        ctf_controls = QHBoxLayout()
        self.open_ctf_viewer_btn_live = QPushButton(">> Open Advanced CTF Viewer")
        self.open_ctf_viewer_btn_live.clicked.connect(self.open_ctf_viewer_live)
        ctf_controls.addWidget(self.open_ctf_viewer_btn_live)
        ctf_controls.addStretch()
        ctf_layout.addLayout(ctf_controls)

        if MATPLOTLIB_AVAILABLE:
            self.ctf_figure = Figure(figsize=(12, 8))
            self.ctf_figure.subplots_adjust(
                left=0.08, right=0.95, top=0.92, bottom=0.12, hspace=0.4, wspace=0.3
            )
            self.ctf_canvas = FigureCanvas(self.ctf_figure)
            ctf_layout.addWidget(self.ctf_canvas)
        else:
            ctf_layout.addWidget(QLabel("Matplotlib not available"))

        self.plot_tabs.addTab(ctf_widget, "🔬 CTF Analysis")

    def create_alignment_tab(self):
        """Create alignment analysis tab."""
        alignment_widget = QWidget()
        alignment_layout = QVBoxLayout(alignment_widget)

        if MATPLOTLIB_AVAILABLE:
            self.alignment_figure = Figure()
            self.alignment_figure.set_size_inches(12, 8)
            self.alignment_canvas = FigureCanvas(self.alignment_figure)
            alignment_layout.addWidget(self.alignment_canvas)
        else:
            alignment_layout.addWidget(QLabel("Matplotlib not available"))

        self.plot_tabs.addTab(alignment_widget, "📐 Alignment Analysis")

    def create_quality_tab(self):
        """Create quality dashboard tab."""
        quality_widget = QWidget()
        quality_layout = QVBoxLayout(quality_widget)

        if MATPLOTLIB_AVAILABLE:
            self.quality_figure = Figure()
            self.quality_figure.set_size_inches(12, 8)
            self.quality_canvas = FigureCanvas(self.quality_figure)
            quality_layout.addWidget(self.quality_canvas)
        else:
            quality_layout.addWidget(QLabel("Matplotlib not available"))

        self.plot_tabs.addTab(quality_widget, "📊 Quality Dashboard")

    # Comparative tab removed as per user request

    def create_series_panel(self, parent):
        """Create the tilt series selection panel."""
        series_widget = QWidget()
        series_layout = QVBoxLayout(series_widget)

        # Header with multi-tomogram support
        header_label = QLabel("🔍 Multi-Tomogram Analysis")
        header_label.setStyleSheet("font-weight: bold; font-size: 14pt; padding: 10px;")
        series_layout.addWidget(header_label)

        # Analysis mode selection
        mode_layout = QHBoxLayout()
        self.analysis_mode = QComboBox()
        self.analysis_mode.addItems(
            ["Latest Results", "Selected Series", "Multi-Series Compare"]
        )
        self.analysis_mode.setCurrentText("Latest Results")
        self.analysis_mode.currentTextChanged.connect(self.on_analysis_mode_changed)
        mode_layout.addWidget(QLabel("Mode:"))
        mode_layout.addWidget(self.analysis_mode)
        series_layout.addLayout(mode_layout)

        # Auto-refresh status
        self.refresh_status = QLabel("🔄 Auto-refresh: ON")
        self.refresh_status.setStyleSheet("color: #27ae60; font-weight: bold;")
        series_layout.addWidget(self.refresh_status)

        # Series tree widget
        self.series_tree = QTreeWidget()
        self.series_tree.setHeaderLabels(["Series", "Status", "Quality"])
        self.series_tree.itemChanged.connect(self.on_series_selection_changed)
        series_layout.addWidget(self.series_tree)

        # Selection summary
        self.selection_summary = QLabel("📊 Active: None")
        series_layout.addWidget(self.selection_summary)

        # Active series display (only show latest/selected, not overlapped)
        self.active_series_label = QLabel("🎯 Active Series: None")
        self.active_series_label.setStyleSheet(
            "font-weight: bold; color: #2980b9; padding: 5px;"
        )
        series_layout.addWidget(self.active_series_label)

        # Quick actions
        actions_layout = QHBoxLayout()

        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.clicked.connect(self.select_all_series)
        actions_layout.addWidget(self.select_all_btn)

        self.clear_all_btn = QPushButton("Clear All")
        self.clear_all_btn.clicked.connect(self.clear_all_series)
        actions_layout.addWidget(self.clear_all_btn)

        series_layout.addLayout(actions_layout)

        # Quality filter
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("Filter:"))

        self.quality_filter = QComboBox()
        self.quality_filter.addItems(["All", "Good Only", "Fair+", "Failed Only"])
        self.quality_filter.currentTextChanged.connect(self.apply_quality_filter)
        filter_layout.addWidget(self.quality_filter)

        series_layout.addLayout(filter_layout)

        # Load results button
        self.load_results_btn = QPushButton("📁 Load Completed Results")
        self.load_results_btn.clicked.connect(self.load_completed_results)
        series_layout.addWidget(self.load_results_btn)

        # Web interface button
        self.web_monitor_btn = QPushButton("🌐 Open Web Monitor")
        self.web_monitor_btn.clicked.connect(self.open_web_monitor)
        series_layout.addWidget(self.web_monitor_btn)

        parent.addWidget(series_widget)

    def connect_signals(self):
        """Connect signals and slots."""
        # Connect to main window signals if available
        if hasattr(self.main_window, "processing_completed"):
            self.main_window.processing_completed.connect(self.on_processing_completed)

        # Connect plot tab changes
        self.plot_tabs.currentChanged.connect(self.on_plot_tab_changed)

    def add_monitoring_directory(self, directory: str):
        """Add a directory for real-time monitoring."""
        if directory and os.path.exists(directory):
            self.monitored_directories.add(directory)
            self.file_watcher.addPath(directory)
            logger.info(f"Added monitoring directory: {directory}")

            # Initial scan
            self.scan_directory_for_series(directory)

    def scan_directory_for_series(self, directory: str):
        """Scan directory for tilt series results."""
        try:
            results_path = Path(directory)

            # Look for AreTomo3 output patterns
            patterns = [
                "*_MC_GL.csv",  # Motion correction
                "*_CTF.txt",  # CTF estimation
                "*_AT_GL.csv",  # Alignment
                "TiltSeries_Metrics.csv",  # Summary metrics
            ]

            found_series = set()

            for pattern in patterns:
                for file_path in results_path.rglob(pattern):
                    # Extract series name from filename
                    if "_MC_GL.csv" in file_path.name:
                        series_name = file_path.stem.replace("_MC_GL", "")
                    elif "_CTF.txt" in file_path.name:
                        series_name = file_path.stem.replace("_CTF", "")
                    elif "_AT_GL.csv" in file_path.name:
                        series_name = file_path.stem.replace("_AT_GL", "")
                    else:
                        continue

                    found_series.add(series_name)

            # Process each found series
            for series_name in found_series:
                self.process_series(series_name, directory)

        except Exception as e:
            logger.error(f"Error scanning directory {directory}: {e}")

    def process_series(self, series_name: str, base_directory: str):
        """Process a tilt series and update data."""
        try:
            # Check if this is a new series or updated
            series_key = f"{base_directory}:{series_name}"

            # Parse AreTomo3 results for this series
            parser = AreTomo3ResultsParser(base_directory)
            parsed_data = parser.parse_all_results()

            # Extract data for this specific series
            series_data = {}
            for data_type, type_data in parsed_data.items():
                if series_name in type_data:
                    series_data[data_type] = type_data[series_name]

            if series_data:
                # Assign color if new series
                if series_name not in self.series_colors:
                    self.series_colors[series_name] = self.color_palette[
                        self.color_index % len(self.color_palette)
                    ]
                    self.color_index += 1

                # Update series data
                self.series_data[series_key] = {
                    "name": series_name,
                    "directory": base_directory,
                    "data": series_data,
                    "last_updated": datetime.now(),
                    "status": self.determine_series_status(series_data),
                    "quality": self.calculate_series_quality(series_data),
                }

                # Update UI
                self.update_series_tree()

                # Generate plots if auto-mode is enabled
                if self.auto_plots_toggle.isChecked():
                    self.update_plots()

                # Extract quality metrics for web interface
                self.extract_quality_metrics(series_name, series_data)

                # Emit signals
                self.series_detected.emit(
                    series_name, self.series_data[series_key]["status"]
                )

                logger.info(
                    f"Processed series: {series_name} with quality: { self.series_data[series_key]['quality']}"
                )

        except Exception as e:
            logger.error(f"Error processing series {series_name}: {e}")

    def determine_series_status(self, series_data: Dict) -> str:
        """Determine the processing status of a series."""
        if not series_data:
            return "❌ Failed"

        has_motion = "motion_data" in series_data and series_data["motion_data"]
        has_ctf = "ctf_data" in series_data and series_data["ctf_data"]
        has_alignment = (
            "alignment_data" in series_data and series_data["alignment_data"]
        )

        if has_motion and has_ctf and has_alignment:
            return "✅ Complete"
        elif has_motion or has_ctf or has_alignment:
            return "⚡ Running"
        else:
            return "🔄 Queued"

    def calculate_series_quality(self, series_data: Dict) -> str:
        """Calculate overall quality of a series."""
        try:
            quality_scores = []

            # Motion quality
            if "motion_data" in series_data:
                motion_data = series_data["motion_data"]
                mean_motion = motion_data.get(
                    "mean_motion", np.mean(motion_data.get("total_motion", [0]))
                )
                if mean_motion < 1.0:
                    quality_scores.append(1.0)
                elif mean_motion < 2.0:
                    quality_scores.append(0.7)
                else:
                    quality_scores.append(0.3)

            # CTF quality
            if "ctf_data" in series_data:
                ctf_data = series_data["ctf_data"]
                cc = ctf_data.get("mean_cc", 0)
                if cc > 0.8:
                    quality_scores.append(1.0)
                elif cc > 0.6:
                    quality_scores.append(0.7)
                else:
                    quality_scores.append(0.3)

            # Alignment quality
            if "alignment_data" in series_data:
                alignment_data = series_data["alignment_data"]
                score = alignment_data.get("mean_score", 0)
                if score > 0.8:
                    quality_scores.append(1.0)
                elif score > 0.5:
                    quality_scores.append(0.7)
                else:
                    quality_scores.append(0.3)

            if quality_scores:
                avg_quality = np.mean(quality_scores)
                if avg_quality > 0.8:
                    return "🟢 Good"
                elif avg_quality > 0.6:
                    return "🟡 Fair"
                else:
                    return "🔴 Poor"
            else:
                return "⏳ Pending"

        except Exception as e:
            logger.error(f"Error calculating series quality: {e}")
            return "❓ Unknown"

    def update_series_tree(self):
        """Update the series tree widget."""
        try:
            self.series_tree.clear()

            selected_count = 0
            total_count = len(self.series_data)

            for series_key, series_info in self.series_data.items():
                item = QTreeWidgetItem(
                    [series_info["name"], series_info["status"], series_info["quality"]]
                )

                # Set checkbox
                item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
                item.setCheckState(0, Qt.CheckState.Checked)
                selected_count += 1

                # Color code by quality
                if "🟢" in series_info["quality"]:
                    item.setBackground(0, QColor(200, 255, 200))
                elif "🟡" in series_info["quality"]:
                    item.setBackground(0, QColor(255, 255, 200))
                elif "🔴" in series_info["quality"]:
                    item.setBackground(0, QColor(255, 200, 200))

                self.series_tree.addTopLevelItem(item)

            # Update selection summary
            self.selection_summary.setText(
                f"📊 Selected: {selected_count}/{total_count} series"
            )

            # Auto-resize columns
            self.series_tree.resizeColumnToContents(0)
            self.series_tree.resizeColumnToContents(1)
            self.series_tree.resizeColumnToContents(2)

        except Exception as e:
            logger.error(f"Error updating series tree: {e}")

    def get_selected_series(self) -> List[str]:
        """Get the active series (only one at a time, no overlapping)."""
        # Return only the active series, not all selected ones
        if self.active_series:
            return [self.active_series]
        elif self.latest_series:
            return [self.latest_series]
        else:
            # If no active series set, return the first checked series
            for i in range(self.series_tree.topLevelItemCount()):
                item = self.series_tree.topLevelItem(i)
                if item.checkState(0) == Qt.CheckState.Checked:
                    series_name = item.text(0)
                    self.set_active_series(series_name)
                    return [series_name]
            return []

    def get_all_selected_series(self) -> List[str]:
        """Get list of all checked series from the tree widget (for reference)."""
        selected_series = []
        for i in range(self.series_tree.topLevelItemCount()):
            item = self.series_tree.topLevelItem(i)
            if item.checkState(0) == Qt.CheckState.Checked:
                selected_series.append(item.text(0))
        return selected_series

    def set_active_series(self, series_name: str):
        """Set the active series for display."""
        self.active_series = series_name
        self.active_series_label.setText(f"🎯 Active Series: {series_name}")
        logger.info(f"Set active series to: {series_name}")

    def on_series_selection_changed(self, item, column):
        """Handle series selection changes."""
        if column == 0:  # Only respond to checkbox changes
            selected_series = self.get_selected_series()
            total_series = self.series_tree.topLevelItemCount()

            self.selection_summary.setText(
                f"📊 Selected: {len(selected_series)}/{total_series} series"
            )

            # Update plots with selected series
            if self.auto_plots_toggle.isChecked():
                self.update_plots()

    def select_all_series(self):
        """Select all series."""
        for i in range(self.series_tree.topLevelItemCount()):
            item = self.series_tree.topLevelItem(i)
            item.setCheckState(0, Qt.CheckState.Checked)

    def clear_all_series(self):
        """Clear all series selections."""
        for i in range(self.series_tree.topLevelItemCount()):
            item = self.series_tree.topLevelItem(i)
            item.setCheckState(0, Qt.CheckState.Unchecked)

    def update_plots(self):
        """Update plots based on selected series."""
        if not MATPLOTLIB_AVAILABLE:
            return

        selected_series = self.get_selected_series()
        if not selected_series:
            return

        current_tab = self.plot_tabs.currentIndex()

        try:
            if current_tab == 0:  # Motion Analysis
                self.update_motion_plots(selected_series)
            elif current_tab == 1:  # CTF Analysis
                self.update_ctf_plots(selected_series)
            elif current_tab == 2:  # Alignment Analysis
                self.update_alignment_plots(selected_series)
            elif current_tab == 3:  # Quality Dashboard
                self.update_quality_plots(selected_series)
            elif current_tab == 4:  # Comparative View
                self.update_comparative_plots(selected_series)

        except Exception as e:
            logger.error(f"Error updating plots: {e}")

    def update_motion_plots(self, selected_series: List[str]):
        """Update motion analysis plots with overlaid series."""
        try:
            self.motion_figure.clear()

            # Create 2x2 subplot layout with proper spacing
            axes = self.motion_figure.subplots(2, 2)
            self.motion_figure.suptitle(
                "Real-time Motion Analysis Comparison", fontsize=14, fontweight="bold"
            )
            self.motion_figure.subplots_adjust(
                left=0.08, right=0.95, top=0.92, bottom=0.12, hspace=0.4, wspace=0.3
            )

            # Debug: Check if we have any data
            logger.info(
                f"Updating motion plots for { len(selected_series)} series: {selected_series}"
            )
            logger.info(
                f"Available series data keys: { list( self.series_data.keys())}"
            )

            # Plot 1: Motion vs Tilt Angle (overlaid)
            ax1 = axes[0, 0]
            plot_data_found = False

            for series_name in selected_series:
                series_data = self.get_series_motion_data(series_name)
                logger.info(
                    f"Motion data for {series_name}: { series_data is not None}"
                )

                if series_data:
                    logger.info(
                        f"Motion data keys for {series_name}: { list( series_data.keys())}"
                    )

                    if "tilt_angles" in series_data and "total_motion" in series_data:
                        color = self.series_colors.get(series_name, "#1f77b4")
                        quality = self.get_series_quality_indicator(series_name)

                        tilt_angles = series_data["tilt_angles"]
                        total_motion = series_data["total_motion"]

                        logger.info(
                            f"Plotting { len(tilt_angles)} points for {series_name}"
                        )

                        ax1.scatter(
                            tilt_angles,
                            total_motion,
                            c=color,
                            alpha=0.7,
                            s=30,
                            label=f"{series_name} {quality}",
                        )
                        plot_data_found = True
                    else:
                        logger.warning(
                            f"Missing required keys for {series_name}: tilt_angles or total_motion"
                        )

            if not plot_data_found:
                # Create sample data for testing
                logger.info("No real data found, creating sample data for testing")
                sample_angles = np.linspace(-60, 60, 41)
                sample_motion = np.random.uniform(0.5, 2.0, 41)
                ax1.scatter(
                    sample_angles,
                    sample_motion,
                    c="blue",
                    alpha=0.7,
                    s=30,
                    label="Sample Data 🔵",
                )
                plot_data_found = True

            ax1.set_xlabel("Tilt Angle (degrees)")
            ax1.set_ylabel("Total Motion (pixels)")
            ax1.set_title("Motion vs Tilt Angle")
            if plot_data_found:
                ax1.legend(loc="upper right", fontsize=8)
            ax1.grid(True, alpha=0.3)

            # Plot 2: Motion Quality Distribution
            ax2 = axes[0, 1]
            quality_counts = {"Good": 0, "Fair": 0, "Poor": 0}
            for series_name in selected_series:
                quality = self.get_series_quality_text(series_name)
                if "Good" in quality or "🟢" in quality:
                    quality_counts["Good"] += 1
                elif "Fair" in quality or "🟡" in quality:
                    quality_counts["Fair"] += 1
                else:
                    quality_counts["Poor"] += 1

            # If no real data, create sample distribution
            if sum(quality_counts.values()) == 0:
                quality_counts = {"Good": 2, "Fair": 1, "Poor": 0}

            # Filter out zero counts for pie chart
            filtered_counts = {k: v for k, v in quality_counts.items() if v > 0}
            if filtered_counts:
                colors = ["#27ae60", "#f39c12", "#e74c3c"]
                pie_colors = [
                    colors[i]
                    for i, k in enumerate(["Good", "Fair", "Poor"])
                    if k in filtered_counts
                ]
                ax2.pie(
                    filtered_counts.values(),
                    labels=filtered_counts.keys(),
                    colors=pie_colors,
                    autopct="%1.1f%%",
                )
            ax2.set_title("Motion Quality Distribution")

            # Plot 3: Motion Trajectory Comparison
            ax3 = axes[1, 0]
            trajectory_data_found = False

            for series_name in selected_series:
                series_data = self.get_series_motion_data(series_name)
                if (
                    series_data
                    and "x_shifts" in series_data
                    and "y_shifts" in series_data
                ):
                    color = self.series_colors.get(series_name, "#1f77b4")
                    ax3.plot(
                        series_data["x_shifts"],
                        series_data["y_shifts"],
                        color=color,
                        alpha=0.7,
                        linewidth=2,
                        label=series_name,
                    )
                    trajectory_data_found = True

            # Add sample trajectory if no real data
            if not trajectory_data_found:
                # Create sample trajectory data
                t = np.linspace(0, 2 * np.pi, 41)
                x_shifts = 2 * np.cos(t) + np.random.normal(0, 0.2, 41)
                y_shifts = 1.5 * np.sin(t) + np.random.normal(0, 0.2, 41)
                ax3.plot(
                    x_shifts,
                    y_shifts,
                    color="blue",
                    alpha=0.7,
                    linewidth=2,
                    label="Sample Trajectory",
                )

            ax3.set_xlabel("X Shift (pixels)")
            ax3.set_ylabel("Y Shift (pixels)")
            ax3.set_title("Motion Trajectories")
            ax3.legend(loc="upper right", fontsize=8)
            ax3.grid(True, alpha=0.3)

            # Plot 4: Motion Statistics
            ax4 = axes[1, 1]
            series_names = []
            mean_motions = []
            max_motions = []

            for series_name in selected_series:
                series_data = self.get_series_motion_data(series_name)
                if series_data:
                    series_names.append(
                        series_name[:8] + "..." if len(series_name) > 8 else series_name
                    )
                    total_motion = series_data.get("total_motion", [])
                    if total_motion:
                        mean_motions.append(np.mean(total_motion))
                        max_motions.append(np.max(total_motion))
                    else:
                        mean_motions.append(series_data.get("mean_motion", 0))
                        max_motions.append(series_data.get("max_motion", 0))

            # Add sample data if no real data
            if not series_names:
                series_names = ["Sample1", "Sample2", "Sample3"]
                mean_motions = [1.2, 0.8, 1.5]
                max_motions = [2.1, 1.4, 2.8]

            if series_names:
                x_pos = np.arange(len(series_names))
                width = 0.35

                bars1 = ax4.bar(
                    x_pos - width / 2,
                    mean_motions,
                    width,
                    label="Mean Motion",
                    alpha=0.7,
                )
                bars2 = ax4.bar(
                    x_pos + width / 2, max_motions, width, label="Max Motion", alpha=0.7
                )

                ax4.set_xlabel("Tilt Series")
                ax4.set_ylabel("Motion (pixels)")
                ax4.set_title("Motion Statistics")
                ax4.set_xticks(x_pos)
                ax4.set_xticklabels(series_names, rotation=45, ha="right", fontsize=8)
                ax4.legend(loc="upper right", fontsize=8)
                ax4.grid(True, alpha=0.3)

            # Force canvas redraw without tight_layout to prevent overlap
            self.motion_canvas.draw()

        except Exception as e:
            logger.error(f"Error updating motion plots: {e}")

    def update_ctf_plots(self, selected_series: List[str]):
        """Update CTF analysis plots with overlaid series."""
        try:
            self.ctf_figure.clear()

            # Create 2x2 subplot layout with proper spacing
            axes = self.ctf_figure.subplots(2, 2)
            self.ctf_figure.suptitle(
                "Real-time CTF Analysis Comparison", fontsize=14, fontweight="bold"
            )
            self.ctf_figure.subplots_adjust(
                left=0.08, right=0.95, top=0.92, bottom=0.12, hspace=0.4, wspace=0.3
            )

            # Plot 1: Defocus vs Tilt Angle (overlaid)
            ax1 = axes[0, 0]
            for series_name in selected_series:
                series_data = self.get_series_ctf_data(series_name)
                if series_data and "tilt_angles" in series_data:
                    color = self.series_colors.get(series_name, "#1f77b4")
                    quality = self.get_series_quality_indicator(series_name)

                    ax1.scatter(
                        series_data["tilt_angles"],
                        series_data["defocus1"],
                        c=color,
                        alpha=0.7,
                        s=30,
                        label=f"{series_name} {quality}",
                    )

            ax1.set_xlabel("Tilt Angle (degrees)")
            ax1.set_ylabel("Defocus (μm)")
            ax1.set_title("Defocus vs Tilt Angle")
            ax1.legend(loc="upper right", fontsize=8)
            ax1.grid(True, alpha=0.3)

            # Plot 2: Resolution Distribution
            ax2 = axes[0, 1]
            all_resolutions = []
            for series_name in selected_series:
                series_data = self.get_series_ctf_data(series_name)
                if series_data and "resolutions" in series_data:
                    # Convert to Angstroms and filter valid resolutions
                    valid_res = [
                        abs(float(r))
                        for r in series_data["resolutions"]
                        if r and float(r) > 0
                    ]
                    all_resolutions.extend(valid_res)

            if all_resolutions:
                # Filter resolutions to meaningful range (2-20Å) and handle
                # outliers
                meaningful_resolutions = [
                    r for r in all_resolutions if 2.0 <= r <= 20.0
                ]
                outliers = [r for r in all_resolutions if r < 2.0 or r > 20.0]

                if meaningful_resolutions:
                    # Use meaningful range for main histogram
                    min_res = min(meaningful_resolutions)
                    max_res = max(meaningful_resolutions)
                    bin_count = min(30, max(10, len(meaningful_resolutions) // 5))

                    ax2.hist(
                        meaningful_resolutions,
                        bins=bin_count,
                        alpha=0.7,
                        edgecolor="black",
                        label=f"Normal ({len(meaningful_resolutions)} values)",
                    )

                    # Add outliers as separate markers if they exist
                    if outliers:
                        # Place at bottom of plot
                        outlier_y = [1] * len(outliers)
                        ax2.scatter(
                            outliers,
                            outlier_y,
                            c="red",
                            marker="x",
                            s=50,
                            label=f"Outliers ({len(outliers)} values)",
                            zorder=5,
                        )

                    ax2.set_xlabel("Resolution (Å)")
                    ax2.set_ylabel("Frequency")
                    ax2.set_title(f"CTF Resolution Distribution (2-20Å range)")
                    ax2.grid(True, alpha=0.3)
                    ax2.legend()

                    # Set meaningful axis limits (2-20Å range)
                    ax2.set_xlim(2.0, 20.0)
                else:
                    # All values are outliers
                    ax2.text(
                        0.5,
                        0.5,
                        f"All { len(outliers)} values are outliers\n(outside 2-20Å range)",
                        transform=ax2.transAxes,
                        ha="center",
                        va="center",
                    )
                    ax2.set_xlabel("Resolution (Å)")
                    ax2.set_ylabel("Frequency")
                    ax2.set_title("CTF Resolution Distribution (No valid range data)")

            # Plot 3: Cross-correlation vs Resolution
            ax3 = axes[1, 0]
            all_resolutions_cc = []
            all_cc_values = []

            for series_name in selected_series:
                series_data = self.get_series_ctf_data(series_name)
                if (
                    series_data
                    and "resolutions" in series_data
                    and "cross_correlations" in series_data
                ):
                    color = self.series_colors.get(series_name, "#1f77b4")

                    # Filter valid data and convert to Angstroms
                    valid_data = [
                        (abs(float(r)), float(cc))
                        for i, (r, cc) in enumerate(
                            zip(
                                series_data["resolutions"],
                                series_data["cross_correlations"],
                            )
                        )
                        if r and float(r) > 0 and cc is not None
                    ]

                    if valid_data:
                        resolutions, cc_values = zip(*valid_data)
                        all_resolutions_cc.extend(resolutions)
                        all_cc_values.extend(cc_values)

                        ax3.scatter(
                            resolutions,
                            cc_values,
                            c=color,
                            alpha=0.7,
                            s=30,
                            label=series_name,
                        )

            ax3.set_xlabel("Resolution (Å)")
            ax3.set_ylabel("Cross-Correlation")
            ax3.set_title("CTF Fit Quality vs Resolution")
            ax3.legend(loc="upper right", fontsize=8)
            ax3.grid(True, alpha=0.3)

            # Set meaningful axis limits (2-20Å range for resolution)
            if all_resolutions_cc and all_cc_values:
                # Use meaningful resolution range (2-20Å)
                ax3.set_xlim(2.0, 20.0)
                ax3.set_ylim(
                    min(all_cc_values) * 0.9, min(1.0, max(all_cc_values) * 1.1)
                )

            # Plot 4: CTF Quality Summary
            ax4 = axes[1, 1]
            series_names = []
            mean_cc_values = []
            mean_resolutions = []

            for series_name in selected_series:
                series_data = self.get_series_ctf_data(series_name)
                if series_data:
                    series_names.append(
                        series_name[:8] + "..." if len(series_name) > 8 else series_name
                    )
                    mean_cc_values.append(series_data.get("mean_cc", 0))
                    mean_resolutions.append(series_data.get("mean_resolution", 0))

            if series_names:
                x_pos = np.arange(len(series_names))
                width = 0.35

                # Normalize for comparison
                norm_cc = np.array(mean_cc_values)
                norm_res = (
                    np.array(mean_resolutions) / max(mean_resolutions)
                    if max(mean_resolutions) > 0
                    else np.zeros_like(mean_resolutions)
                )

                bars1 = ax4.bar(
                    x_pos - width / 2,
                    norm_cc,
                    width,
                    label="Cross-Correlation",
                    alpha=0.7,
                )
                bars2 = ax4.bar(
                    x_pos + width / 2,
                    norm_res,
                    width,
                    label="Resolution (norm)",
                    alpha=0.7,
                )

                ax4.set_xlabel("Tilt Series")
                ax4.set_ylabel("Quality Score")
                ax4.set_title("CTF Quality Summary")
                ax4.set_xticks(x_pos)
                ax4.set_xticklabels(series_names, rotation=45, ha="right", fontsize=8)
                ax4.legend(loc="upper right", fontsize=8)
                ax4.grid(True, alpha=0.3)

            # Force canvas redraw without tight_layout to prevent overlap
            self.ctf_canvas.draw()

        except Exception as e:
            logger.error(f"Error updating CTF plots: {e}")

    def get_series_motion_data(self, series_name: str) -> Optional[Dict]:
        """Get motion data for a specific series."""
        for series_key, series_info in self.series_data.items():
            if series_info["name"] == series_name:
                return series_info["data"].get("motion_data")
        return None

    def get_series_ctf_data(self, series_name: str) -> Optional[Dict]:
        """Get CTF data for a specific series."""
        for series_key, series_info in self.series_data.items():
            if series_info["name"] == series_name:
                return series_info["data"].get("ctf_data")
        return None

    def get_series_alignment_data(self, series_name: str) -> Optional[Dict]:
        """Get alignment data for a specific series."""
        for series_key, series_info in self.series_data.items():
            if series_info["name"] == series_name:
                return series_info["data"].get("alignment_data")
        return None

    def get_series_quality_indicator(self, series_name: str) -> str:
        """Get quality indicator emoji for a series."""
        for series_key, series_info in self.series_data.items():
            if series_info["name"] == series_name:
                quality = series_info["quality"]
                if "🟢" in quality:
                    return "🟢"
                elif "🟡" in quality:
                    return "🟡"
                elif "🔴" in quality:
                    return "🔴"
        return "⏳"

    def get_series_quality_text(self, series_name: str) -> str:
        """Get quality text for a series."""
        for series_key, series_info in self.series_data.items():
            if series_info["name"] == series_name:
                return series_info["quality"]
        return "Unknown"

    def extract_quality_metrics(self, series_name: str, series_data: Dict):
        """Extract quality metrics for web interface."""
        try:
            metrics = {
                "series": series_name,
                "timestamp": datetime.now().isoformat(),
                "motion": {},
                "ctf": {},
                "alignment": {},
                "overall_quality": "Unknown",
            }

            # Motion metrics
            if "motion_data" in series_data:
                motion_data = series_data["motion_data"]
                metrics["motion"] = {
                    "mean_motion": motion_data.get("mean_motion", 0),
                    "max_motion": motion_data.get("max_motion", 0),
                    "drift_rate": motion_data.get("drift_rate", 0),
                    "quality": (
                        "Good"
                        if motion_data.get("mean_motion", 0) < 1.0
                        else (
                            "Fair"
                            if motion_data.get("mean_motion", 0) < 2.0
                            else "Poor"
                        )
                    ),
                }

            # CTF metrics
            if "ctf_data" in series_data:
                ctf_data = series_data["ctf_data"]
                metrics["ctf"] = {
                    "mean_defocus": ctf_data.get("mean_defocus_value", 0),
                    "mean_astigmatism": ctf_data.get("mean_astigmatism", 0),
                    "mean_resolution": ctf_data.get("mean_resolution", 0),
                    "mean_cc": ctf_data.get("mean_cc", 0),
                    "quality": (
                        "Good"
                        if ctf_data.get("mean_cc", 0) > 0.8
                        else "Fair" if ctf_data.get("mean_cc", 0) > 0.6 else "Poor"
                    ),
                }

            # Alignment metrics
            if "alignment_data" in series_data:
                alignment_data = series_data["alignment_data"]
                metrics["alignment"] = {
                    "mean_score": alignment_data.get("mean_score", 0),
                    "mean_shift": alignment_data.get("mean_shift", 0),
                    "tilt_axis_stability": alignment_data.get("std_tilt_axis", 0),
                    "num_tilts": alignment_data.get("num_tilts", 0),
                    "quality": (
                        "Good"
                        if alignment_data.get("mean_score", 0) > 0.8
                        else (
                            "Fair"
                            if alignment_data.get("mean_score", 0) > 0.5
                            else "Poor"
                        )
                    ),
                }

            # Calculate overall quality
            quality_scores = []
            for category in ["motion", "ctf", "alignment"]:
                if category in metrics and "quality" in metrics[category]:
                    if metrics[category]["quality"] == "Good":
                        quality_scores.append(1.0)
                    elif metrics[category]["quality"] == "Fair":
                        quality_scores.append(0.7)
                    else:
                        quality_scores.append(0.3)

            if quality_scores:
                avg_quality = np.mean(quality_scores)
                if avg_quality > 0.8:
                    metrics["overall_quality"] = "Excellent"
                elif avg_quality > 0.6:
                    metrics["overall_quality"] = "Good"
                elif avg_quality > 0.4:
                    metrics["overall_quality"] = "Fair"
                else:
                    metrics["overall_quality"] = "Poor"

            # Store metrics
            self.quality_metrics[series_name] = metrics

            # Emit signal for web interface
            self.quality_updated.emit(metrics)

        except Exception as e:
            logger.error(f"Error extracting quality metrics for {series_name}: {e}")

    def update_real_time_data(self):
        """Update real-time data from monitored directories."""
        if not self.realtime_toggle.isChecked():
            return

        try:
            for directory in self.monitored_directories.copy():
                if os.path.exists(directory):
                    self.scan_directory_for_series(directory)
                else:
                    # Remove non-existent directories
                    self.monitored_directories.discard(directory)
                    self.file_watcher.removePath(directory)

        except Exception as e:
            logger.error(f"Error in real-time data update: {e}")

    def on_directory_changed(self, path: str):
        """Handle directory change events."""
        if path in self.monitored_directories:
            logger.info(f"Directory changed: {path}")
            self.scan_directory_for_series(path)

    def toggle_realtime_updates(self, enabled: bool):
        """Toggle real-time updates."""
        if enabled:
            self.update_timer.start(5000)
            self.refresh_status.setText("🔄 Auto-refresh: ON")
            self.refresh_status.setStyleSheet("color: #27ae60; font-weight: bold;")
        else:
            self.update_timer.stop()
            self.refresh_status.setText("⏸️ Auto-refresh: OFF")
            self.refresh_status.setStyleSheet("color: #e74c3c; font-weight: bold;")

    def on_plot_tab_changed(self, index: int):
        """Handle plot tab changes."""
        if self.auto_plots_toggle.isChecked():
            self.update_plots()

    def on_analysis_mode_changed(self, mode):
        """Handle analysis mode change for multi-tomogram support."""
        logger.info(f"Analysis mode changed to: {mode}")

        if mode == "Latest Results":
            # Show only the most recent results without overlap
            self.show_latest_results_only()
        elif mode == "Selected Series":
            # Show only selected series without overlap
            self.show_selected_series_only()
        elif mode == "Multi-Series Compare":
            # Enable multi-series comparison
            self.enable_multi_series_comparison()

        # Update plots based on new mode
        self.update_plots()

    def show_latest_results_only(self):
        """Show only the latest results without overlap."""
        if self.series_data:
            # Find the most recent series
            latest_series = max(
                self.series_data.items(), key=lambda x: x[1]["last_updated"]
            )
            self.active_series = latest_series[0]
            self.active_series_label.setText(
                f"🎯 Active Series: {latest_series[1]['name']}"
            )
            logger.info(f"Showing latest results for: { latest_series[1]['name']}")

    def show_selected_series_only(self):
        """Show only selected series without overlap."""
        selected_series = []
        for i in range(self.series_tree.topLevelItemCount()):
            item = self.series_tree.topLevelItem(i)
            if item.checkState(0) == Qt.CheckState.Checked:
                series_name = item.text(0)
                selected_series.append(series_name)

        if selected_series:
            # Use the first selected series as active
            self.active_series = selected_series[0]
            self.active_series_label.setText(f"🎯 Active Series: {selected_series[0]}")
            logger.info(f"Showing selected series: {selected_series}")
        else:
            self.active_series_label.setText("🎯 Active Series: None selected")

    def enable_multi_series_comparison(self):
        """Enable multi-series comparison mode."""
        selected_count = 0
        for i in range(self.series_tree.topLevelItemCount()):
            item = self.series_tree.topLevelItem(i)
            if item.checkState(0) == Qt.CheckState.Checked:
                selected_count += 1

        self.active_series_label.setText(f"🎯 Comparing: {selected_count} series")
        logger.info(f"Multi-series comparison enabled for {selected_count} series")

    def apply_quality_filter(self, filter_text: str):
        """Apply quality filter to series tree."""
        try:
            for i in range(self.series_tree.topLevelItemCount()):
                item = self.series_tree.topLevelItem(i)
                quality = item.text(2)

                show_item = True
                if filter_text == "Good Only" and "🟢" not in quality:
                    show_item = False
                elif filter_text == "Fair+" and "🔴" in quality:
                    show_item = False
                elif filter_text == "Failed Only" and "❌" not in item.text(1):
                    show_item = False

                item.setHidden(not show_item)

        except Exception as e:
            logger.error(f"Error applying quality filter: {e}")

    def export_all_plots(self):
        """Export all plots as PNG and SVG files."""
        try:
            export_dir = QFileDialog.getExistingDirectory(
                self, "Select Export Directory", str(Path.home())
            )

            if not export_dir:
                return

            export_path = Path(export_dir)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Export each plot type
            plot_types = ["motion", "ctf", "alignment", "quality", "comparative"]
            selected_series = self.get_selected_series()

            if not selected_series:
                QMessageBox.warning(
                    self, "No Selection", "Please select at least one tilt series."
                )
                return

            exported_files = []

            for i, plot_type in enumerate(plot_types):
                # Switch to plot tab and update
                self.plot_tabs.setCurrentIndex(i)
                self.update_plots()

                # Get the appropriate figure
                if plot_type == "motion" and hasattr(self, "motion_figure"):
                    figure = self.motion_figure
                elif plot_type == "ctf" and hasattr(self, "ctf_figure"):
                    figure = self.ctf_figure
                elif plot_type == "alignment" and hasattr(self, "alignment_figure"):
                    figure = self.alignment_figure
                elif plot_type == "quality" and hasattr(self, "quality_figure"):
                    figure = self.quality_figure
                elif plot_type == "comparative" and hasattr(self, "comparative_figure"):
                    figure = self.comparative_figure
                else:
                    continue

                # Export PNG
                png_file = (
                    export_path / f"aretomo3_{plot_type}_analysis_{timestamp}.png"
                )
                figure.savefig(png_file, dpi=300, bbox_inches="tight")
                exported_files.append(str(png_file))

                # Export SVG for web scaling
                svg_file = (
                    export_path / f"aretomo3_{plot_type}_analysis_{timestamp}.svg"
                )
                figure.savefig(svg_file, format="svg", bbox_inches="tight")
                exported_files.append(str(svg_file))

            QMessageBox.information(
                self,
                "Export Complete",
                f"Exported {len(exported_files)} files to:\n{export_dir}",
            )

        except Exception as e:
            logger.error(f"Error exporting plots: {e}")
            QMessageBox.critical(
                self, "Export Error", f"Failed to export plots: {str(e)}"
            )

    def open_web_monitor(self):
        """Open web monitoring interface."""
        try:
            # Check if web server is running
            if hasattr(self.main_window, "web_server_widget"):
                web_widget = self.main_window.web_server_widget
                if hasattr(web_widget, "is_running") and web_widget.is_running:
                    # Get server URL
                    port = getattr(web_widget, "port", 8080)
                    url = f"http://localhost:{port}/analysis"

                    # Open in browser
                    import webbrowser

                    webbrowser.open(url)
                else:
                    QMessageBox.information(
                        self,
                        "Web Server",
                        "Web server is not running. Please start it from the Control Center.",
                    )
            else:
                QMessageBox.warning(
                    self, "Web Server", "Web server widget not available."
                )

        except Exception as e:
            logger.error(f"Error opening web monitor: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to open web monitor: { str(e)}",
            )

    def generate_pdf_reports(self):
        """Generate PDF reports for selected tilt series."""
        try:
            selected_series = self.get_selected_series()
            if not selected_series:
                QMessageBox.warning(
                    self, "No Selection", "Please select at least one tilt series."
                )
                return

            # Import PDF generator
            from ...utils.pdf_report_generator import TomogramPDFReportGenerator

            if not hasattr(TomogramPDFReportGenerator, "__init__"):
                QMessageBox.warning(
                    self,
                    "PDF Generation",
                    "PDF report generation not available (ReportLab not installed).",
                )
                return

            # Select output directory
            output_dir = QFileDialog.getExistingDirectory(
                self, "Select PDF Output Directory", str(Path.home())
            )

            if not output_dir:
                return

            pdf_generator = TomogramPDFReportGenerator()
            generated_reports = []

            for series_name in selected_series:
                # Get series data and quality metrics
                series_data = {}
                for series_key, series_info in self.series_data.items():
                    if series_info["name"] == series_name:
                        series_data = series_info["data"]
                        break

                quality_metrics = self.quality_metrics.get(series_name, {})

                if series_data:
                    pdf_path = pdf_generator.generate_report(
                        series_name, series_data, quality_metrics, output_dir
                    )

                    if pdf_path:
                        generated_reports.append(pdf_path)
                        self.pdf_generated.emit(series_name, pdf_path)

            if generated_reports:
                QMessageBox.information(
                    self,
                    "PDF Reports Generated",
                    f"Generated { len(generated_reports)} PDF reports in:\n{output_dir}",
                )
            else:
                QMessageBox.warning(
                    self,
                    "PDF Generation Failed",
                    "No PDF reports were generated. Check the logs for details.",
                )

        except Exception as e:
            logger.error(f"Error generating PDF reports: {e}")
            QMessageBox.critical(
                self, "Error", f"Failed to generate PDF reports: {str(e)}"
            )

    def on_processing_completed(self, job_id: str, result_path: str):
        """Handle processing completion signal from main window."""
        try:
            # Extract series name from result path
            result_path_obj = Path(result_path)
            series_name = result_path_obj.stem

            # Add monitoring for the result directory
            if result_path_obj.parent.exists():
                self.add_monitoring_directory(str(result_path_obj.parent))

        except Exception as e:
            logger.error(f"Error handling processing completion: {e}")

    def update_alignment_plots(self, selected_series: List[str]):
        """Update alignment analysis plots with overlaid series."""
        try:
            self.alignment_figure.clear()

            # Create 2x2 subplot layout
            axes = self.alignment_figure.subplots(2, 2)
            self.alignment_figure.suptitle(
                "Real-time Alignment Analysis Comparison",
                fontsize=14,
                fontweight="bold",
            )

            # Plot 1: Alignment Score vs Tilt Angle (overlaid)
            ax1 = axes[0, 0]
            for series_name in selected_series:
                series_data = self.get_series_alignment_data(series_name)
                if series_data and "tilt_angles" in series_data:
                    color = self.series_colors.get(series_name, "#1f77b4")
                    quality = self.get_series_quality_indicator(series_name)

                    tilt_angles = series_data["tilt_angles"]
                    scores = series_data.get("scores", [])

                    # Ensure arrays have the same length
                    min_len = min(len(tilt_angles), len(scores))
                    if min_len > 0:
                        ax1.scatter(
                            tilt_angles[:min_len],
                            scores[:min_len],
                            c=color,
                            alpha=0.7,
                            s=30,
                            label=f"{series_name} {quality}",
                        )

            ax1.set_xlabel("Tilt Angle (degrees)")
            ax1.set_ylabel("Alignment Score")
            ax1.set_title("Alignment Score vs Tilt Angle")
            ax1.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
            ax1.grid(True, alpha=0.3)

            # Plot 2: Tilt Axis Stability
            ax2 = axes[0, 1]
            for series_name in selected_series:
                series_data = self.get_series_alignment_data(series_name)
                if series_data and "tilt_angles" in series_data:
                    color = self.series_colors.get(series_name, "#1f77b4")
                    tilt_angles = series_data["tilt_angles"]
                    tilt_axis_values = series_data.get("tilt_axis", [])

                    if tilt_axis_values:
                        # Ensure arrays have the same length
                        min_len = min(len(tilt_angles), len(tilt_axis_values))
                        if min_len > 0:
                            ax2.plot(
                                tilt_angles[:min_len],
                                tilt_axis_values[:min_len],
                                color=color,
                                alpha=0.7,
                                linewidth=2,
                                label=series_name,
                            )

            ax2.set_xlabel("Tilt Angle (degrees)")
            ax2.set_ylabel("Tilt Axis (degrees)")
            ax2.set_title("Tilt Axis Stability")
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # Plot 3: X/Y Shifts vs Tilt Angle
            ax3 = axes[1, 0]
            for series_name in selected_series:
                series_data = self.get_series_alignment_data(series_name)
                if series_data and "tilt_angles" in series_data:
                    color = self.series_colors.get(series_name, "#1f77b4")
                    tilt_angles = series_data["tilt_angles"]
                    x_shifts = series_data.get("x_shifts", [])
                    y_shifts = series_data.get("y_shifts", [])

                    if x_shifts:
                        # Ensure arrays have the same length
                        min_len = min(len(tilt_angles), len(x_shifts))
                        if min_len > 0:
                            ax3.plot(
                                tilt_angles[:min_len],
                                x_shifts[:min_len],
                                color=color,
                                alpha=0.7,
                                linewidth=2,
                                linestyle="-",
                                label=f"{series_name} X",
                            )
                    if y_shifts:
                        # Ensure arrays have the same length
                        min_len = min(len(tilt_angles), len(y_shifts))
                        if min_len > 0:
                            ax3.plot(
                                tilt_angles[:min_len],
                                y_shifts[:min_len],
                                color=color,
                                alpha=0.7,
                                linewidth=2,
                                linestyle="--",
                                label=f"{series_name} Y",
                            )

            ax3.set_xlabel("Tilt Angle (degrees)")
            ax3.set_ylabel("Shift (pixels)")
            ax3.set_title("Alignment Shifts vs Tilt Angle")
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # Plot 4: Alignment Quality Summary
            ax4 = axes[1, 1]
            series_names = []
            mean_scores = []
            std_scores = []

            for series_name in selected_series:
                series_data = self.get_series_alignment_data(series_name)
                if series_data:
                    series_names.append(
                        series_name[:8] + "..." if len(series_name) > 8 else series_name
                    )
                    scores = series_data.get("scores", [])
                    if scores:
                        mean_scores.append(np.mean(scores))
                        std_scores.append(np.std(scores))
                    else:
                        mean_scores.append(0)
                        std_scores.append(0)

            if series_names:
                x_pos = np.arange(len(series_names))
                bars = ax4.bar(
                    x_pos, mean_scores, yerr=std_scores, alpha=0.7, capsize=5
                )

                ax4.set_xlabel("Tilt Series")
                ax4.set_ylabel("Mean Alignment Score")
                ax4.set_title("Alignment Quality Summary")
                ax4.set_xticks(x_pos)
                ax4.set_xticklabels(series_names, rotation=45, ha="right")
                ax4.grid(True, alpha=0.3)

            plt.tight_layout()
            self.alignment_canvas.draw()

        except Exception as e:
            logger.error(f"Error updating alignment plots: {e}")

    def update_quality_plots(self, selected_series: List[str]):
        """Update quality dashboard plots."""
        try:
            self.quality_figure.clear()

            # Create 2x2 subplot layout
            axes = self.quality_figure.subplots(2, 2)
            self.quality_figure.suptitle(
                "Real-time Quality Dashboard", fontsize=14, fontweight="bold"
            )

            # Plot 1: Overall Quality Score vs Tilt Angle
            ax1 = axes[0, 0]
            for series_name in selected_series:
                # Calculate quality score for each tilt angle
                motion_data = self.get_series_motion_data(series_name)
                ctf_data = self.get_series_ctf_data(series_name)
                alignment_data = self.get_series_alignment_data(series_name)

                if motion_data and "tilt_angles" in motion_data:
                    color = self.series_colors.get(series_name, "#1f77b4")
                    quality = self.get_series_quality_indicator(series_name)

                    # Calculate combined quality score per tilt angle
                    quality_scores = []
                    for i, angle in enumerate(motion_data["tilt_angles"]):
                        score = 0.5  # Base score

                        # Motion contribution
                        if i < len(motion_data.get("total_motion", [])):
                            motion = motion_data["total_motion"][i]
                            score += 0.3 * (
                                1.0 if motion < 1.0 else 0.5 if motion < 2.0 else 0.1
                            )

                        # CTF contribution
                        if ctf_data and i < len(ctf_data.get("cross_correlations", [])):
                            cc = ctf_data["cross_correlations"][i]
                            score += 0.2 * cc

                        quality_scores.append(score)

                    ax1.plot(
                        motion_data["tilt_angles"],
                        quality_scores,
                        color=color,
                        alpha=0.7,
                        linewidth=2,
                        label=f"{series_name} {quality}",
                    )

            ax1.set_xlabel("Tilt Angle (degrees)")
            ax1.set_ylabel("Quality Score")
            ax1.set_title("Quality Score vs Tilt Angle")
            ax1.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
            ax1.grid(True, alpha=0.3)
            ax1.set_ylim(0, 1)

            # Plot 2: Quality Distribution Pie Chart
            ax2 = axes[0, 1]
            quality_counts = {"Excellent": 0, "Good": 0, "Fair": 0, "Poor": 0}
            for series_name in selected_series:
                quality_text = self.get_series_quality_text(series_name)
                if "Excellent" in quality_text or "🟢" in quality_text:
                    quality_counts["Excellent"] += 1
                elif "Good" in quality_text:
                    quality_counts["Good"] += 1
                elif "Fair" in quality_text or "🟡" in quality_text:
                    quality_counts["Fair"] += 1
                else:
                    quality_counts["Poor"] += 1

            # Filter out zero counts
            filtered_counts = {k: v for k, v in quality_counts.items() if v > 0}
            if filtered_counts:
                colors = {
                    "Excellent": "#2ecc71",
                    "Good": "#27ae60",
                    "Fair": "#f39c12",
                    "Poor": "#e74c3c",
                }
                pie_colors = [colors[k] for k in filtered_counts.keys()]
                ax2.pie(
                    filtered_counts.values(),
                    labels=filtered_counts.keys(),
                    colors=pie_colors,
                    autopct="%1.1f%%",
                    startangle=90,
                )
                ax2.set_title("Quality Distribution")

            # Plot 3: Quality Metrics Comparison
            ax3 = axes[1, 0]
            series_names = []
            motion_scores = []
            ctf_scores = []
            alignment_scores = []

            for series_name in selected_series:
                series_names.append(
                    series_name[:8] + "..." if len(series_name) > 8 else series_name
                )

                # Get quality metrics
                metrics = self.quality_metrics.get(series_name, {})
                motion_quality = metrics.get("motion", {}).get("quality", "Poor")
                ctf_quality = metrics.get("ctf", {}).get("quality", "Poor")
                alignment_quality = metrics.get("alignment", {}).get("quality", "Poor")

                # Convert to scores
                def quality_to_score(q):
                    """Execute quality_to_score operation."""
                    return 1.0 if q == "Good" else 0.7 if q == "Fair" else 0.3

                motion_scores.append(quality_to_score(motion_quality))
                ctf_scores.append(quality_to_score(ctf_quality))
                alignment_scores.append(quality_to_score(alignment_quality))

            if series_names:
                x_pos = np.arange(len(series_names))
                width = 0.25

                bars1 = ax3.bar(
                    x_pos - width, motion_scores, width, label="Motion", alpha=0.7
                )
                bars2 = ax3.bar(x_pos, ctf_scores, width, label="CTF", alpha=0.7)
                bars3 = ax3.bar(
                    x_pos + width, alignment_scores, width, label="Alignment", alpha=0.7
                )

                ax3.set_xlabel("Tilt Series")
                ax3.set_ylabel("Quality Score")
                ax3.set_title("Quality Metrics Comparison")
                ax3.set_xticks(x_pos)
                ax3.set_xticklabels(series_names, rotation=45, ha="right")
                ax3.legend()
                ax3.grid(True, alpha=0.3)
                ax3.set_ylim(0, 1.1)

            # Plot 4: Processing Success Rate
            ax4 = axes[1, 1]
            total_series = len(selected_series)
            successful_series = 0
            failed_series = 0

            for series_name in selected_series:
                quality_text = self.get_series_quality_text(series_name)
                if "❌" in quality_text or "Failed" in quality_text:
                    failed_series += 1
                else:
                    successful_series += 1

            if total_series > 0:
                success_rate = successful_series / total_series * 100
                failure_rate = failed_series / total_series * 100

                categories = ["Successful", "Failed"]
                values = [success_rate, failure_rate]
                colors = ["#27ae60", "#e74c3c"]

                bars = ax4.bar(categories, values, color=colors, alpha=0.7)
                ax4.set_ylabel("Percentage (%)")
                ax4.set_title("Processing Success Rate")
                ax4.set_ylim(0, 100)

                # Add percentage labels on bars
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax4.text(
                        bar.get_x() + bar.get_width() / 2.0,
                        height + 1,
                        f"{value:.1f}%",
                        ha="center",
                        va="bottom",
                    )

            plt.tight_layout()
            self.quality_canvas.draw()

        except Exception as e:
            logger.error(f"Error updating quality plots: {e}")

    def update_comparative_plots(self, selected_series: List[str]):
        """Update comparative view plots with all metrics overlaid."""
        try:
            self.comparative_figure.clear()

            # Create 3x2 subplot layout for comprehensive comparison
            axes = self.comparative_figure.subplots(3, 2)
            self.comparative_figure.suptitle(
                "Comprehensive Multi-Series Comparison", fontsize=16, fontweight="bold"
            )

            # Plot 1: Motion vs Tilt Angle (all series overlaid)
            ax1 = axes[0, 0]
            for series_name in selected_series:
                series_data = self.get_series_motion_data(series_name)
                if series_data and "tilt_angles" in series_data:
                    color = self.series_colors.get(series_name, "#1f77b4")
                    quality = self.get_series_quality_indicator(series_name)
                    ax1.plot(
                        series_data["tilt_angles"],
                        series_data.get("total_motion", []),
                        color=color,
                        alpha=0.8,
                        linewidth=2,
                        label=f"{series_name} {quality}",
                    )

            ax1.set_xlabel("Tilt Angle (degrees)")
            ax1.set_ylabel("Total Motion (pixels)")
            ax1.set_title("Motion vs Tilt Angle")
            ax1.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
            ax1.grid(True, alpha=0.3)

            # Plot 2: Defocus vs Tilt Angle (all series overlaid)
            ax2 = axes[0, 1]
            for series_name in selected_series:
                series_data = self.get_series_ctf_data(series_name)
                if series_data and "tilt_angles" in series_data:
                    color = self.series_colors.get(series_name, "#1f77b4")
                    quality = self.get_series_quality_indicator(series_name)
                    ax2.plot(
                        series_data["tilt_angles"],
                        series_data.get("defocus1", []),
                        color=color,
                        alpha=0.8,
                        linewidth=2,
                        label=f"{series_name} {quality}",
                    )

            ax2.set_xlabel("Tilt Angle (degrees)")
            ax2.set_ylabel("Defocus (μm)")
            ax2.set_title("Defocus vs Tilt Angle")
            ax2.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
            ax2.grid(True, alpha=0.3)

            # Plot 3: CTF Resolution vs Tilt Angle
            ax3 = axes[1, 0]
            all_valid_resolutions = []

            for series_name in selected_series:
                series_data = self.get_series_ctf_data(series_name)
                if series_data and "tilt_angles" in series_data:
                    color = self.series_colors.get(series_name, "#1f77b4")
                    resolutions = series_data.get("resolutions", [])
                    if resolutions:
                        # Filter out invalid resolutions and convert to
                        # Angstroms
                        valid_data = [
                            (angle, abs(float(r)))
                            for i, (angle, r) in enumerate(
                                zip(series_data["tilt_angles"], resolutions)
                            )
                            if r and float(r) > 0
                        ]

                        if valid_data:
                            valid_angles, valid_resolutions = zip(*valid_data)
                            all_valid_resolutions.extend(valid_resolutions)
                            ax3.plot(
                                valid_angles,
                                valid_resolutions,
                                color=color,
                                alpha=0.8,
                                linewidth=2,
                                label=series_name,
                            )

            ax3.set_xlabel("Tilt Angle (degrees)")
            ax3.set_ylabel("Resolution (Å)")
            ax3.set_title("CTF Resolution vs Tilt Angle")
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # Auto-scale y-axis for resolution
            if all_valid_resolutions:
                min_res = min(all_valid_resolutions)
                max_res = max(all_valid_resolutions)
                ax3.set_ylim(min_res * 0.9, max_res * 1.1)

            # Plot 4: Alignment Score vs Tilt Angle
            ax4 = axes[1, 1]
            for series_name in selected_series:
                series_data = self.get_series_alignment_data(series_name)
                if series_data and "tilt_angles" in series_data:
                    color = self.series_colors.get(series_name, "#1f77b4")
                    scores = series_data.get("scores", [])
                    if scores:
                        ax4.plot(
                            series_data["tilt_angles"],
                            scores,
                            color=color,
                            alpha=0.8,
                            linewidth=2,
                            label=series_name,
                        )

            ax4.set_xlabel("Tilt Angle (degrees)")
            ax4.set_ylabel("Alignment Score")
            ax4.set_title("Alignment Score vs Tilt Angle")
            ax4.legend()
            ax4.grid(True, alpha=0.3)

            # Plot 5: Combined Quality Metrics Heatmap
            ax5 = axes[2, 0]
            if selected_series:
                # Create quality matrix
                metrics_names = ["Motion", "CTF", "Alignment"]
                quality_matrix = []

                for series_name in selected_series:
                    metrics = self.quality_metrics.get(series_name, {})
                    row = []
                    for metric in ["motion", "ctf", "alignment"]:
                        quality = metrics.get(metric, {}).get("quality", "Poor")
                        score = (
                            1.0
                            if quality == "Good"
                            else 0.7 if quality == "Fair" else 0.3
                        )
                        row.append(score)
                    quality_matrix.append(row)

                if quality_matrix:
                    im = ax5.imshow(
                        quality_matrix, cmap="RdYlGn", aspect="auto", vmin=0, vmax=1
                    )
                    ax5.set_xticks(range(len(metrics_names)))
                    ax5.set_xticklabels(metrics_names)
                    ax5.set_yticks(range(len(selected_series)))
                    ax5.set_yticklabels(
                        [s[:10] + "..." if len(s) > 10 else s for s in selected_series]
                    )
                    ax5.set_title("Quality Metrics Heatmap")

                    # Add colorbar
                    cbar = plt.colorbar(im, ax=ax5)
                    cbar.set_label("Quality Score")

            # Plot 6: Processing Statistics Summary
            ax6 = axes[2, 1]
            series_names = [s[:8] + "..." if len(s) > 8 else s for s in selected_series]
            processing_times = []
            success_rates = []

            for series_name in selected_series:
                # Get processing time (mock data for now)
                processing_times.append(
                    np.random.uniform(5, 30)
                )  # Mock processing time

                # Calculate success rate based on quality
                quality_text = self.get_series_quality_text(series_name)
                if "❌" in quality_text or "Failed" in quality_text:
                    success_rates.append(0)
                else:
                    success_rates.append(100)

            if series_names:
                x_pos = np.arange(len(series_names))

                # Create twin axis for processing time
                ax6_twin = ax6.twinx()

                bars1 = ax6.bar(
                    x_pos,
                    success_rates,
                    alpha=0.7,
                    color="#27ae60",
                    label="Success Rate (%)",
                )
                line1 = ax6_twin.plot(
                    x_pos,
                    processing_times,
                    "ro-",
                    alpha=0.8,
                    label="Processing Time (min)",
                )

                ax6.set_xlabel("Tilt Series")
                ax6.set_ylabel("Success Rate (%)", color="#27ae60")
                ax6_twin.set_ylabel("Processing Time (min)", color="red")
                ax6.set_title("Processing Statistics")
                ax6.set_xticks(x_pos)
                ax6.set_xticklabels(series_names, rotation=45, ha="right")
                ax6.set_ylim(0, 100)
                ax6.grid(True, alpha=0.3)

            plt.tight_layout()
            self.comparative_canvas.draw()

        except Exception as e:
            logger.error(f"Error updating comparative plots: {e}")

    def load_completed_results(self):
        """Load completed AreTomo3 results from a directory."""
        try:
            # Ask user to select results directory
            results_dir = QFileDialog.getExistingDirectory(
                self,
                "Select AreTomo3 Results Directory",
                str(Path.home()),
                QFileDialog.Option.ShowDirsOnly,
            )

            if not results_dir:
                return

            logger.info(f"Loading completed results from: {results_dir}")

            # Show progress message
            self.refresh_status.setText("🔄 Loading results...")
            self.refresh_status.setStyleSheet("color: #17a2b8; font-weight: bold;")

            # Parse results using AreTomo3ResultsParser
            parser = AreTomo3ResultsParser(results_dir)
            parsed_data = parser.parse_all_results()

            if not parsed_data:
                QMessageBox.warning(
                    self,
                    "No Results",
                    "No AreTomo3 results found in the selected directory.",
                )
                self.refresh_status.setText("❌ No results found")
                self.refresh_status.setStyleSheet("color: #dc3545; font-weight: bold;")
                return

            # Process each series found in the results
            series_count = 0
            motion_data = parsed_data.get("motion_data", {})
            ctf_data = parsed_data.get("ctf_data", {})
            alignment_data = parsed_data.get("alignment_data", {})

            # Get all unique series names
            all_series = set()
            all_series.update(motion_data.keys())
            all_series.update(ctf_data.keys())
            all_series.update(alignment_data.keys())

            logger.info(f"Found {len(all_series)} series: {list(all_series)}")

            for series_name in all_series:
                # Combine data for this series
                series_data = {}

                # Add motion data
                if series_name in motion_data:
                    series_data["motion_data"] = motion_data[series_name]

                # Add CTF data with tilt angles from MDOC files
                if series_name in ctf_data:
                    ctf_series_data = ctf_data[series_name].copy()

                    # Parse tilt angles from MDOC file for this series
                    mdoc_tilt_angles = self.parse_mdoc_tilt_angles(
                        results_dir, series_name
                    )

                    if mdoc_tilt_angles:
                        # Map micrograph numbers to tilt angles from MDOC
                        micrograph_numbers = ctf_series_data.get(
                            "micrograph_numbers", []
                        )
                        if micrograph_numbers:
                            # Create tilt angles array based on micrograph
                            # numbers and MDOC data
                            ctf_tilt_angles = []
                            for mic_num in micrograph_numbers:
                                # Micrograph numbers are 1-based, convert to
                                # 0-based index
                                idx = mic_num - 1
                                if 0 <= idx < len(mdoc_tilt_angles):
                                    ctf_tilt_angles.append(mdoc_tilt_angles[idx])
                                else:
                                    # Fallback: estimate based on position
                                    if mdoc_tilt_angles:
                                        ctf_tilt_angles.append(
                                            mdoc_tilt_angles[
                                                min(idx, len(mdoc_tilt_angles) - 1)
                                            ]
                                        )

                            ctf_series_data["tilt_angles"] = ctf_tilt_angles
                            logger.info(
                                f"Mapped { len(ctf_tilt_angles)} CTF tilt angles from MDOC for {series_name}"
                            )
                    else:
                        # Fallback to motion data if MDOC parsing fails
                        if (
                            series_name in motion_data
                            and "tilt_angles" in motion_data[series_name]
                        ):
                            motion_tilt_angles = motion_data[series_name]["tilt_angles"]
                            micrograph_numbers = ctf_series_data.get(
                                "micrograph_numbers", []
                            )

                            if micrograph_numbers and len(micrograph_numbers) <= len(
                                motion_tilt_angles
                            ):
                                ctf_tilt_angles = [
                                    (
                                        motion_tilt_angles[i]
                                        if i < len(motion_tilt_angles)
                                        else 0
                                    )
                                    for i in range(len(micrograph_numbers))
                                ]
                                ctf_series_data["tilt_angles"] = ctf_tilt_angles
                                logger.info(
                                    f"Fallback: Used motion tilt angles for CTF data for {series_name}"
                                )

                    series_data["ctf_data"] = ctf_series_data

                # Add alignment data
                if series_name in alignment_data:
                    series_data["alignment_data"] = alignment_data[series_name]

                if series_data:
                    # Assign color if new series
                    if series_name not in self.series_colors:
                        self.series_colors[series_name] = self.color_palette[
                            self.color_index % len(self.color_palette)
                        ]
                        self.color_index += 1

                    # Create series key
                    series_key = f"{results_dir}:{series_name}"

                    # Store series data
                    self.series_data[series_key] = {
                        "name": series_name,
                        "directory": results_dir,
                        "data": series_data,
                        "last_updated": datetime.now(),
                        "status": self.determine_series_status(series_data),
                        "quality": self.calculate_series_quality(series_data),
                    }

                    # Extract quality metrics for web interface
                    self.extract_quality_metrics(series_name, series_data)

                    series_count += 1

            # Update UI
            self.update_series_tree()

            # Generate plots if auto-mode is enabled
            if self.auto_plots_toggle.isChecked():
                self.update_plots()

            # Update status
            self.refresh_status.setText(f"✅ Loaded {series_count} series")
            self.refresh_status.setStyleSheet("color: #28a745; font-weight: bold;")

            # Add directory to monitoring
            self.add_monitoring_directory(results_dir)

            QMessageBox.information(
                self,
                "Results Loaded",
                f"Successfully loaded {series_count} tilt series from:\n{results_dir}\n\n"
                f"Motion data: {len(motion_data)} series\n"
                f"CTF data: {len(ctf_data)} series\n"
                f"Alignment data: {len(alignment_data)} series",
            )

            logger.info(f"Successfully loaded {series_count} series from {results_dir}")

        except Exception as e:
            logger.error(f"Error loading completed results: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to load results: { str(e)}",
            )
            self.refresh_status.setText("❌ Load failed")
            self.refresh_status.setStyleSheet("color: #dc3545; font-weight: bold;")

    # Live Processing Mode Support Methods
    def set_live_processing_mode(self, enabled: bool):
        """Set live processing mode."""
        self.live_processing_mode = enabled
        if enabled:
            # Update UI for live processing mode
            if hasattr(self, "refresh_status"):
                self.refresh_status.setText("🔴 Live Mode: Waiting for data")
                self.refresh_status.setStyleSheet("color: #dc3545; font-weight: bold;")
            logger.info("Live processing mode enabled")
        else:
            if hasattr(self, "refresh_status"):
                self.refresh_status.setText("🔄 Auto-refresh: ON")
                self.refresh_status.setStyleSheet("color: #27ae60; font-weight: bold;")
            logger.info("Live processing mode disabled")

    def set_monitoring_directory(self, directory: str):
        """Set the directory to monitor for live processing."""
        self.monitoring_directory = directory
        if directory and os.path.exists(directory):
            self.add_monitoring_directory(directory)
            logger.info(f"Set monitoring directory for live processing: {directory}")
        else:
            logger.warning(f"Invalid monitoring directory: {directory}")

    def start_monitoring(self):
        """Start monitoring for live processing."""
        if self.live_processing_mode and self.monitoring_directory:
            # Update status
            if hasattr(self, "refresh_status"):
                self.refresh_status.setText("🟢 Live Mode: Monitoring active")
                self.refresh_status.setStyleSheet("color: #28a745; font-weight: bold;")

            # Start real-time updates
            if hasattr(self, "realtime_toggle"):
                self.realtime_toggle.setChecked(True)

            # Scan for existing data
            self.scan_directory_for_series(self.monitoring_directory)

            logger.info("Started live processing monitoring")
        else:
            logger.warning(
                "Cannot start monitoring: live mode not enabled or no directory set"
            )

    def stop_monitoring(self):
        """Stop monitoring for live processing."""
        if hasattr(self, "refresh_status"):
            self.refresh_status.setText("🔴 Live Mode: Monitoring stopped")
            self.refresh_status.setStyleSheet("color: #dc3545; font-weight: bold;")

        # Stop real-time updates
        if hasattr(self, "realtime_toggle"):
            self.realtime_toggle.setChecked(False)

        logger.info("Stopped live processing monitoring")

    def update_live_status(self, status: str, style_class: str = "info"):
        """Update live processing status."""
        if hasattr(self, "refresh_status"):
            status_colors = {
                "success": "#28a745",
                "warning": "#ffc107",
                "error": "#dc3545",
                "info": "#17a2b8",
            }
            color = status_colors.get(style_class, "#17a2b8")
            self.refresh_status.setText(f"🔄 Live: {status}")
            self.refresh_status.setStyleSheet(f"color: {color}; font-weight: bold;")

    def parse_mdoc_tilt_angles(self, results_dir: str, series_name: str) -> List[float]:
        """Parse tilt angles from MDOC file for accurate CTF plotting."""
        try:
            # Look for MDOC file in various locations
            possible_mdoc_paths = [
                os.path.join(results_dir, f"{series_name}.mdoc"),
                os.path.join(results_dir, "..", f"{series_name}.mdoc"),
                os.path.join(results_dir, "..", series_name, f"{series_name}.mdoc"),
                os.path.join(os.path.dirname(results_dir), f"{series_name}.mdoc"),
                os.path.join(
                    os.path.dirname(results_dir), series_name, f"{series_name}.mdoc"
                ),
            ]

            mdoc_file = None
            for path in possible_mdoc_paths:
                if os.path.exists(path):
                    mdoc_file = path
                    break

            if not mdoc_file:
                logger.warning(
                    f"MDOC file not found for {series_name} in any expected location"
                )
                return []

            logger.info(f"Parsing MDOC file: {mdoc_file}")

            tilt_angles = []
            with open(mdoc_file) as f:
                for line in f:
                    line = line.strip()
                    if line.startswith("TiltAngle"):
                        # Extract tilt angle value
                        parts = line.split("=")
                        if len(parts) == 2:
                            try:
                                angle = float(parts[1].strip())
                                tilt_angles.append(angle)
                            except ValueError:
                                logger.warning(f"Could not parse tilt angle: {line}")

            logger.info(
                f"Parsed { len(tilt_angles)} tilt angles from MDOC for {series_name}"
            )
            return tilt_angles

        except Exception as e:
            logger.error(f"Error parsing MDOC file for {series_name}: {e}")
            return []

    def open_ctf_viewer_live(self):
        """Open the advanced CTF visualizer for live processing."""
        try:
            # Get selected series
            selected_series = self.get_selected_series()
            if not selected_series:
                QMessageBox.warning(
                    self,
                    "No Series Selected",
                    "Please select a tilt series to view CTF data.",
                )
                return

            # Check if we have CTF data for the selected series
            if selected_series not in self.series_data:
                QMessageBox.warning(
                    self, "No Data", "No data available for the selected series."
                )
                return

            series_data = self.series_data[selected_series]

            # Check if we have CTF parameters
            if "ctf_parameters" not in series_data or not series_data["ctf_parameters"]:
                QMessageBox.warning(
                    self,
                    "No CTF Data",
                    "No CTF parameters found for the selected series.",
                )
                return

            # Try to import and create CTF visualizer
            try:
                from ...analysis.ctf_analysis.ctf_visualizer import CTF2DVisualizer

                # Extract CTF data in the expected format
                ctf_data = {
                    "series_name": selected_series,
                    "parameters": series_data.get("ctf_parameters", {}),
                    "power_spectra": series_data.get("power_spectra"),
                    "tilt_angles": series_data.get("tilt_angles", [])
                }

                # Create and show CTF visualizer
                self.ctf_visualizer_live = CTF2DVisualizer(ctf_data)
                fig = self.ctf_visualizer_live.create_interactive_viewer()

                # Show the figure

                plt.show()

                logger.info(
                    "Advanced CTF visualizer opened successfully (Live Processing)"
                )

            except ImportError as ie:
                logger.warning(f"CTF visualizer not available: {ie}")
                QMessageBox.warning(
                    self,
                    "Visualizer Not Available",
                    "Advanced CTF visualizer is not available.",
                )
            except Exception as ve:
                logger.error(f"Error creating CTF visualizer: {ve}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to create CTF visualizer: { str(ve)}",
                )

        except Exception as e:
            logger.error(f"Error opening CTF viewer (Live): {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to open CTF viewer: { str(e)}",
            )

    def open_motion_viewer_live(self):
        """Open the advanced motion visualizer for live processing."""
        try:
            # Get selected series
            selected_series = self.get_selected_series()
            if not selected_series:
                QMessageBox.warning(
                    self,
                    "No Series Selected",
                    "Please select a tilt series to view motion data.",
                )
                return

            # Check if we have motion data for the selected series
            if selected_series not in self.series_data:
                QMessageBox.warning(
                    self, "No Data", "No data available for the selected series."
                )
                return

            series_data = self.series_data[selected_series]

            # Check if we have motion data
            if "motion_data" not in series_data or not series_data["motion_data"]:
                QMessageBox.warning(
                    self,
                    "No Motion Data",
                    "No motion correction data found for the selected series.",
                )
                return

            motion_data = series_data["motion_data"]

            # Try to import and create Motion visualizer
            try:
                from ...analysis.motion_analysis.motion_visualizer import (
                    MotionCorrectionVisualizer,
                )

                # Extract motion data in the expected format
                formatted_motion_data = {
                    "series_name": selected_series,
                    "motion_data": motion_data,
                    "tilt_angles": series_data.get("tilt_angles", [])
                }

                # Create and show motion visualizer
                self.motion_visualizer_live = MotionCorrectionVisualizer(formatted_motion_data)
                fig = self.motion_visualizer_live.create_interactive_viewer()

                # Show the figure

                plt.show()

                logger.info(
                    "Advanced Motion visualizer opened successfully (Live Processing)"
                )

            except ImportError as ie:
                logger.warning(f"Motion visualizer not available: {ie}")
                QMessageBox.warning(
                    self,
                    "Visualizer Not Available",
                    "Advanced Motion visualizer is not available.",
                )
            except Exception as ve:
                logger.error(f"Error creating Motion visualizer: {ve}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to create Motion visualizer: { str(ve)}",
                )

        except Exception as e:
            logger.error(f"Error opening Motion viewer (Live): {e}")
            QMessageBox.critical(
                self, "Error", f"Failed to open Motion viewer: {str(e)}"
            )
