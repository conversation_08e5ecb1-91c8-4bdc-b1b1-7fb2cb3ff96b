#!/usr/bin/env python3
"""
Napari Viewer Tab for AreTomo3 GUI
Integrates Napari for 3D volume visualization with MRC file support
"""

import logging
import os
from pathlib import Path
from typing import Optional, Dict, Any

from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QFormLayout,
    QPushButton, QLabel, QFileDialog, QMessageBox, QProgressBar,
    QListWidget, QSplitter, QTextEdit, QCheckBox, QSpinBox,
    QDoubleSpinBox, QComboBox, QSlider
)

logger = logging.getLogger(__name__)

# Check for Napari availability
try:
    import napari
    from napari.qt import create_worker
    NAPARI_AVAILABLE = True
    logger.info("Napari is available for 3D visualization")
except ImportError:
    NAPARI_AVAILABLE = False
    logger.warning("Napari not available - install with: pip install napari[all]")

# Check for MRC file reader
try:
    import mrcfile
    MRC_AVAILABLE = True
except ImportError:
    MRC_AVAILABLE = False
    logger.warning("mrcfile not available - install with: pip install mrcfile")


class NapariViewerTab(QWidget):
    """Napari integration tab for 3D volume visualization."""
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.napari_viewer = None
        self.current_data = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Main splitter (no duplicate header - main window already has AreTomo3-GUI header)
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel - Controls (smaller)
        left_panel = self.create_control_panel()
        left_panel.setMaximumWidth(300)  # Limit control panel width
        splitter.addWidget(left_panel)

        # Right panel - Napari viewer (full size)
        right_panel = self.create_viewer_panel()
        splitter.addWidget(right_panel)

        # Set splitter proportions (20% controls, 80% viewer)
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 4)
        
        layout.addWidget(splitter)
        
    def create_control_panel(self):
        """Create the control panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # File loading group
        file_group = QGroupBox("📁 File Loading")
        file_layout = QVBoxLayout(file_group)
        
        # Load MRC file button
        self.load_mrc_btn = QPushButton("📂 Load MRC File")
        self.load_mrc_btn.clicked.connect(self.load_mrc_file)
        file_layout.addWidget(self.load_mrc_btn)
        
        # Load from results button
        self.load_results_btn = QPushButton("📊 Load from Results")
        self.load_results_btn.clicked.connect(self.load_from_results)
        file_layout.addWidget(self.load_results_btn)
        
        # File info
        self.file_info = QLabel("No file loaded")
        self.file_info.setWordWrap(True)
        self.file_info.setStyleSheet("padding: 5px; border: 1px solid #ccc; border-radius: 3px;")
        file_layout.addWidget(self.file_info)
        
        layout.addWidget(file_group)
        
        # Visualization controls
        viz_group = QGroupBox("🎨 Visualization")
        viz_layout = QFormLayout(viz_group)
        
        # Contrast limits
        self.contrast_min = QDoubleSpinBox()
        self.contrast_min.setRange(-1000, 1000)
        self.contrast_min.setValue(0)
        viz_layout.addRow("Contrast Min:", self.contrast_min)
        
        self.contrast_max = QDoubleSpinBox()
        self.contrast_max.setRange(-1000, 1000)
        self.contrast_max.setValue(100)
        viz_layout.addRow("Contrast Max:", self.contrast_max)
        
        # Colormap
        self.colormap_combo = QComboBox()
        self.colormap_combo.addItems(["gray", "viridis", "plasma", "inferno", "magma"])
        viz_layout.addRow("Colormap:", self.colormap_combo)
        
        # Apply visualization button
        self.apply_viz_btn = QPushButton("🎨 Apply Visualization")
        self.apply_viz_btn.clicked.connect(self.apply_visualization)
        self.apply_viz_btn.setEnabled(False)
        viz_layout.addRow(self.apply_viz_btn)
        
        layout.addWidget(viz_group)
        
        # Napari controls
        napari_group = QGroupBox("🔬 Napari Controls")
        napari_layout = QVBoxLayout(napari_group)
        
        # Launch Napari button
        self.launch_napari_btn = QPushButton("🚀 Launch Napari Viewer")
        self.launch_napari_btn.clicked.connect(self.launch_napari)
        self.launch_napari_btn.setEnabled(NAPARI_AVAILABLE and MRC_AVAILABLE)
        napari_layout.addWidget(self.launch_napari_btn)
        
        # Status
        self.napari_status = QLabel("Napari not launched")
        napari_layout.addWidget(self.napari_status)
        
        layout.addWidget(napari_group)
        
        # Installation help
        if not NAPARI_AVAILABLE or not MRC_AVAILABLE:
            help_group = QGroupBox("📦 Installation")
            help_layout = QVBoxLayout(help_group)
            
            help_text = QTextEdit()
            help_text.setMaximumHeight(100)
            help_text.setReadOnly(True)
            
            install_text = "To use Napari viewer, install required packages:\n\n"
            if not NAPARI_AVAILABLE:
                install_text += "pip install napari[all]\n"
            if not MRC_AVAILABLE:
                install_text += "pip install mrcfile\n"
            install_text += "\nThen restart the application."
            
            help_text.setPlainText(install_text)
            help_layout.addWidget(help_text)
            
            layout.addWidget(help_group)
        
        layout.addStretch()
        return panel
        
    def create_viewer_panel(self):
        """Create the viewer panel with embedded Napari."""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        if NAPARI_AVAILABLE:
            try:
                # Try to create embedded Napari viewer
                from napari.qt import create_worker
                from qtpy.QtWidgets import QWidget as QtWidget

                # Create embedded Napari viewer
                self.napari_viewer = napari.Viewer(show=False)
                self.napari_widget = self.napari_viewer.window._qt_window

                # Embed the Napari widget
                layout.addWidget(self.napari_widget)

                # Update status
                self.napari_status.setText("✅ Napari viewer embedded")
                self.launch_napari_btn.setText("🔄 Reset Viewer")

                logger.info("Napari viewer embedded successfully")

            except Exception as e:
                logger.warning(f"Could not embed Napari viewer: {e}")
                # Fallback to placeholder
                self.viewer_placeholder = QLabel(
                    "🔬 Napari Viewer\n\n"
                    "Click 'Launch Napari Viewer' to open in separate window\n\n"
                    "Features:\n"
                    "• 3D volume rendering\n"
                    "• MRC file support\n"
                    "• Interactive visualization\n"
                    "• Layer management\n"
                    "• Export capabilities"
                )
                self.viewer_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
                self.viewer_placeholder.setStyleSheet(
                    "border: 2px dashed #ccc; padding: 50px; font-size: 14px; color: #666;"
                )
                layout.addWidget(self.viewer_placeholder)
        else:
            self.viewer_placeholder = QLabel(
                "🔬 Napari Viewer (Not Available)\n\n"
                "Install Napari to enable 3D visualization:\n"
                "pip install napari[all]\n"
                "pip install mrcfile\n\n"
                "Features when installed:\n"
                "• 3D volume rendering\n"
                "• MRC file support\n"
                "• Interactive visualization\n"
                "• Layer management\n"
                "• Export capabilities"
            )
            self.viewer_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.viewer_placeholder.setStyleSheet(
                "border: 2px dashed #ccc; padding: 50px; font-size: 14px; color: #666;"
            )
            layout.addWidget(self.viewer_placeholder)

        return panel
        
    def load_mrc_file(self):
        """Load an MRC file."""
        if not MRC_AVAILABLE:
            QMessageBox.warning(
                self, "MRC Support Missing",
                "mrcfile package not available. Install with:\npip install mrcfile"
            )
            return
            
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select MRC File", "", "MRC Files (*.mrc *.mrcs);;All Files (*)"
        )
        
        if file_path:
            self.load_mrc_data(file_path)
            
    def load_from_results(self):
        """Load MRC files from results directory."""
        if not hasattr(self.main_window, 'current_results_dir') or not self.main_window.current_results_dir:
            QMessageBox.information(
                self, "No Results",
                "No results directory available. Please run processing first."
            )
            return
            
        # Find MRC files in results directory
        results_dir = Path(self.main_window.current_results_dir)
        mrc_files = list(results_dir.glob("*.mrc")) + list(results_dir.glob("*.mrcs"))
        
        if not mrc_files:
            QMessageBox.information(
                self, "No MRC Files",
                "No MRC files found in results directory."
            )
            return
            
        # For now, load the first MRC file found
        self.load_mrc_data(str(mrc_files[0]))
        
    def load_mrc_data(self, file_path: str):
        """Load MRC data from file."""
        try:
            import mrcfile
            
            with mrcfile.open(file_path, mode='r') as mrc:
                self.current_data = mrc.data.copy()
                
            # Update file info
            file_info = f"File: {Path(file_path).name}\n"
            file_info += f"Shape: {self.current_data.shape}\n"
            file_info += f"Data type: {self.current_data.dtype}\n"
            file_info += f"Min/Max: {self.current_data.min():.2f} / {self.current_data.max():.2f}"
            
            self.file_info.setText(file_info)
            
            # Update contrast limits
            self.contrast_min.setValue(float(self.current_data.min()))
            self.contrast_max.setValue(float(self.current_data.max()))
            
            # Enable visualization controls
            self.apply_viz_btn.setEnabled(True)
            
            logger.info(f"Loaded MRC file: {file_path}")
            
        except Exception as e:
            logger.error(f"Error loading MRC file: {e}")
            QMessageBox.critical(
                self, "Load Error",
                f"Failed to load MRC file:\n{str(e)}"
            )
            
    def launch_napari(self):
        """Launch Napari viewer."""
        if not NAPARI_AVAILABLE:
            QMessageBox.warning(
                self, "Napari Not Available",
                "Napari not installed. Install with:\npip install napari[all]"
            )
            return
            
        try:
            # Create Napari viewer
            self.napari_viewer = napari.Viewer()
            
            # Add current data if available
            if self.current_data is not None:
                self.napari_viewer.add_image(
                    self.current_data,
                    name="Volume",
                    colormap=self.colormap_combo.currentText(),
                    contrast_limits=[self.contrast_min.value(), self.contrast_max.value()]
                )
                
            self.napari_status.setText("✅ Napari viewer launched")
            logger.info("Napari viewer launched successfully")
            
        except Exception as e:
            logger.error(f"Error launching Napari: {e}")
            QMessageBox.critical(
                self, "Napari Error",
                f"Failed to launch Napari:\n{str(e)}"
            )
            
    def apply_visualization(self):
        """Apply visualization settings."""
        if self.napari_viewer is not None and self.current_data is not None:
            try:
                # Update the image layer
                if len(self.napari_viewer.layers) > 0:
                    layer = self.napari_viewer.layers[0]
                    layer.colormap = self.colormap_combo.currentText()
                    layer.contrast_limits = [self.contrast_min.value(), self.contrast_max.value()]
                    
                logger.info("Visualization settings applied")
                
            except Exception as e:
                logger.error(f"Error applying visualization: {e}")
                QMessageBox.warning(
                    self, "Visualization Error",
                    f"Failed to apply settings:\n{str(e)}"
                )
