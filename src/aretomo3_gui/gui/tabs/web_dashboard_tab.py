#!/usr/bin/env python3
"""
Web Dashboard Tab for AreTomo3 GUI
Integrated web interface and real-time monitoring dashboard.
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QGroupBox, QFormLayout, QSpinBox, QLineEdit,
    QCheckBox, QComboBox, QTabWidget, QScrollArea, QFrame,
    QSplitter, QTableWidget, QTableWidgetItem, QHeaderView,
    QProgressBar, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QUrl
from PyQt6.QtGui import QFont, QPixmap, QPalette

logger = logging.getLogger(__name__)

# Try to import web engine for embedded browser
try:
    from PyQt6.QtWebEngineWidgets import QWebEngineView
    WEB_ENGINE_AVAILABLE = True
except ImportError:
    WEB_ENGINE_AVAILABLE = False
    logger.warning("QtWebEngine not available - embedded browser disabled")

# Try to import web server components
try:
    from aretomo3_gui.web.server import WebServer, WebInterface
    WEB_SERVER_AVAILABLE = True
except ImportError:
    WEB_SERVER_AVAILABLE = False
    logger.warning("Web server components not available")


class WebServerManager(QThread):
    """Thread to manage web server operations."""
    
    status_updated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.web_interface = None
        self.is_running = False
        self.port = 8000
        self.websocket_port = 8001
    
    def start_web_server(self, port: int = 8000, websocket_port: int = 8001):
        """Start the web server."""
        if not WEB_SERVER_AVAILABLE:
            self.error_occurred.emit("Web server components not available")
            return
        
        try:
            self.port = port
            self.websocket_port = websocket_port
            self.web_interface = WebInterface(web_port=port, websocket_port=websocket_port)
            
            if self.web_interface.start():
                self.is_running = True
                self.status_updated.emit({
                    "status": "running",
                    "port": port,
                    "websocket_port": websocket_port,
                    "url": f"http://localhost:{port}"
                })
                logger.info(f"Web server started on port {port}")
            else:
                self.error_occurred.emit("Failed to start web server")
                
        except Exception as e:
            self.error_occurred.emit(f"Web server error: {str(e)}")
    
    def stop_web_server(self):
        """Stop the web server."""
        if self.web_interface:
            try:
                self.web_interface.stop()
                self.is_running = False
                self.status_updated.emit({"status": "stopped"})
                logger.info("Web server stopped")
            except Exception as e:
                self.error_occurred.emit(f"Error stopping web server: {str(e)}")
    
    def update_processing_status(self, job_id: str, status: Dict[str, Any]):
        """Update processing status in web interface."""
        if self.web_interface:
            self.web_interface.update_processing_status(job_id, status)
    
    def update_analysis_results(self, dataset_id: str, results: Dict[str, Any]):
        """Update analysis results in web interface."""
        if self.web_interface:
            self.web_interface.update_analysis_results(dataset_id, results)


class WebDashboardTab(QWidget):
    """Web Dashboard integration tab."""
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.web_server_manager = WebServerManager()
        self.web_view = None
        self.server_status = {"status": "stopped"}
        
        # Connect signals
        self.web_server_manager.status_updated.connect(self.on_server_status_updated)
        self.web_server_manager.error_occurred.connect(self.on_server_error)
        
        self.setup_ui()
        self.setup_monitoring()
        
        logger.info("Web Dashboard Tab initialized")
    
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Header
        header_label = QLabel("🌐 Web Dashboard & Remote Access")
        header_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(header_label)
        
        # Create splitter for main content
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # Left panel - Controls and Status
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Web View or Status
        right_panel = self.create_web_view_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
    
    def create_control_panel(self) -> QWidget:
        """Create the control panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Server Control Group
        server_group = QGroupBox("Web Server Control")
        server_layout = QFormLayout(server_group)
        
        # Port settings
        self.port_spinbox = QSpinBox()
        self.port_spinbox.setRange(1024, 65535)
        self.port_spinbox.setValue(8000)
        server_layout.addRow("Web Port:", self.port_spinbox)
        
        self.websocket_port_spinbox = QSpinBox()
        self.websocket_port_spinbox.setRange(1024, 65535)
        self.websocket_port_spinbox.setValue(8001)
        server_layout.addRow("WebSocket Port:", self.websocket_port_spinbox)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("🚀 Start Server")
        self.start_button.clicked.connect(self.start_web_server)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("🛑 Stop Server")
        self.stop_button.clicked.connect(self.stop_web_server)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        button_layout.addWidget(self.stop_button)
        
        server_layout.addRow(button_layout)
        
        layout.addWidget(server_group)
        
        # Status Group
        status_group = QGroupBox("Server Status")
        status_layout = QFormLayout(status_group)
        
        self.status_label = QLabel("Stopped")
        self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        status_layout.addRow("Status:", self.status_label)
        
        self.url_label = QLabel("Not running")
        self.url_label.setStyleSheet("color: #7f8c8d;")
        status_layout.addRow("URL:", self.url_label)
        
        self.clients_label = QLabel("0")
        status_layout.addRow("Connected Clients:", self.clients_label)
        
        layout.addWidget(status_group)
        
        # Features Group
        features_group = QGroupBox("Dashboard Features")
        features_layout = QVBoxLayout(features_group)
        
        features_text = QLabel("""
• Real-time processing monitoring
• Live quality assessment plots
• System resource monitoring
• Remote job submission
• Analysis results visualization
• Export and download capabilities
• WebSocket real-time updates
• REST API access
        """)
        features_text.setStyleSheet("color: #34495e; line-height: 1.4;")
        features_layout.addWidget(features_text)
        
        layout.addWidget(features_group)
        
        # Quick Actions Group
        actions_group = QGroupBox("Quick Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        self.open_browser_button = QPushButton("🌐 Open in Browser")
        self.open_browser_button.clicked.connect(self.open_in_browser)
        self.open_browser_button.setEnabled(False)
        actions_layout.addWidget(self.open_browser_button)
        
        self.refresh_button = QPushButton("🔄 Refresh View")
        self.refresh_button.clicked.connect(self.refresh_web_view)
        self.refresh_button.setEnabled(False)
        actions_layout.addWidget(self.refresh_button)
        
        self.api_docs_button = QPushButton("📚 API Documentation")
        self.api_docs_button.clicked.connect(self.show_api_docs)
        self.api_docs_button.setEnabled(False)
        actions_layout.addWidget(self.api_docs_button)
        
        layout.addWidget(actions_group)
        
        # Add stretch to push everything to top
        layout.addStretch()
        
        return panel
    
    def create_web_view_panel(self) -> QWidget:
        """Create the web view panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        if WEB_ENGINE_AVAILABLE:
            # Create embedded web browser
            self.web_view = QWebEngineView()
            self.web_view.setUrl(QUrl("about:blank"))
            
            # Create placeholder page
            placeholder_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>AreTomo3 GUI Dashboard</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        text-align: center;
                        padding: 50px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        margin: 0;
                    }
                    .container {
                        background: rgba(255, 255, 255, 0.1);
                        padding: 40px;
                        border-radius: 10px;
                        backdrop-filter: blur(10px);
                    }
                    h1 { margin-bottom: 20px; }
                    .status { font-size: 18px; margin: 20px 0; }
                    .instructions { margin-top: 30px; opacity: 0.8; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🌐 AreTomo3 GUI Web Dashboard</h1>
                    <div class="status">Web server is not running</div>
                    <div class="instructions">
                        Click "Start Server" to launch the web interface<br>
                        and access real-time monitoring capabilities.
                    </div>
                </div>
            </body>
            </html>
            """
            self.web_view.setHtml(placeholder_html)
            layout.addWidget(self.web_view)
            
        else:
            # Fallback when web engine is not available
            fallback_label = QLabel("""
            🌐 Web Dashboard View
            
            QtWebEngine not available - embedded browser disabled.
            
            To enable embedded web view:
            pip install PyQt6-WebEngine
            
            You can still use the web dashboard by:
            1. Starting the web server
            2. Opening the URL in your external browser
            """)
            fallback_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            fallback_label.setStyleSheet("""
                QLabel {
                    background-color: #ecf0f1;
                    border: 2px dashed #bdc3c7;
                    border-radius: 8px;
                    padding: 40px;
                    color: #7f8c8d;
                    font-size: 14px;
                    line-height: 1.6;
                }
            """)
            layout.addWidget(fallback_label)
        
        return panel
    
    def setup_monitoring(self):
        """Set up monitoring timers."""
        # Status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_display)
        self.status_timer.start(2000)  # Update every 2 seconds
    
    def start_web_server(self):
        """Start the web server."""
        port = self.port_spinbox.value()
        websocket_port = self.websocket_port_spinbox.value()
        
        self.start_button.setEnabled(False)
        self.start_button.setText("🔄 Starting...")
        
        self.web_server_manager.start_web_server(port, websocket_port)
    
    def stop_web_server(self):
        """Stop the web server."""
        self.stop_button.setEnabled(False)
        self.stop_button.setText("🔄 Stopping...")
        
        self.web_server_manager.stop_web_server()
    
    def on_server_status_updated(self, status: Dict[str, Any]):
        """Handle server status updates."""
        self.server_status = status
        
        if status.get("status") == "running":
            self.status_label.setText("Running")
            self.status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            
            url = status.get("url", "")
            self.url_label.setText(f'<a href="{url}">{url}</a>')
            self.url_label.setOpenExternalLinks(True)
            self.url_label.setStyleSheet("color: #3498db;")
            
            # Update buttons
            self.start_button.setEnabled(False)
            self.start_button.setText("🚀 Start Server")
            self.stop_button.setEnabled(True)
            self.stop_button.setText("🛑 Stop Server")
            
            # Enable action buttons
            self.open_browser_button.setEnabled(True)
            self.refresh_button.setEnabled(True)
            self.api_docs_button.setEnabled(True)
            
            # Load web interface in embedded view
            if self.web_view and WEB_ENGINE_AVAILABLE:
                self.web_view.setUrl(QUrl(url))
                
        elif status.get("status") == "stopped":
            self.status_label.setText("Stopped")
            self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            self.url_label.setText("Not running")
            self.url_label.setStyleSheet("color: #7f8c8d;")
            
            # Update buttons
            self.start_button.setEnabled(True)
            self.start_button.setText("🚀 Start Server")
            self.stop_button.setEnabled(False)
            self.stop_button.setText("🛑 Stop Server")
            
            # Disable action buttons
            self.open_browser_button.setEnabled(False)
            self.refresh_button.setEnabled(False)
            self.api_docs_button.setEnabled(False)
            
            # Show placeholder in web view
            if self.web_view and WEB_ENGINE_AVAILABLE:
                placeholder_html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>AreTomo3 GUI Dashboard</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            text-align: center;
                            padding: 50px;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            margin: 0;
                        }
                        .container {
                            background: rgba(255, 255, 255, 0.1);
                            padding: 40px;
                            border-radius: 10px;
                            backdrop-filter: blur(10px);
                        }
                        h1 { margin-bottom: 20px; }
                        .status { font-size: 18px; margin: 20px 0; }
                        .instructions { margin-top: 30px; opacity: 0.8; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🌐 AreTomo3 GUI Web Dashboard</h1>
                        <div class="status">Web server is not running</div>
                        <div class="instructions">
                            Click "Start Server" to launch the web interface<br>
                            and access real-time monitoring capabilities.
                        </div>
                    </div>
                </body>
                </html>
                """
                self.web_view.setHtml(placeholder_html)
    
    def on_server_error(self, error_message: str):
        """Handle server errors."""
        QMessageBox.critical(self, "Web Server Error", error_message)
        
        # Reset button states
        self.start_button.setEnabled(True)
        self.start_button.setText("🚀 Start Server")
        self.stop_button.setEnabled(False)
        self.stop_button.setText("🛑 Stop Server")
    
    def update_status_display(self):
        """Update status display periodically."""
        # This could be expanded to show more detailed status
        pass
    
    def open_in_browser(self):
        """Open the web interface in external browser."""
        if self.server_status.get("status") == "running":
            url = self.server_status.get("url", "")
            if url:
                import webbrowser
                webbrowser.open(url)
    
    def refresh_web_view(self):
        """Refresh the embedded web view."""
        if self.web_view and WEB_ENGINE_AVAILABLE:
            self.web_view.reload()
    
    def show_api_docs(self):
        """Show API documentation."""
        if self.server_status.get("status") == "running":
            url = self.server_status.get("url", "")
            if url:
                api_url = f"{url}/api"
                if self.web_view and WEB_ENGINE_AVAILABLE:
                    self.web_view.setUrl(QUrl(api_url))
                else:
                    import webbrowser
                    webbrowser.open(api_url)
    
    def update_processing_status(self, job_id: str, status: Dict[str, Any]):
        """Update processing status in web interface."""
        self.web_server_manager.update_processing_status(job_id, status)
    
    def update_analysis_results(self, dataset_id: str, results: Dict[str, Any]):
        """Update analysis results in web interface."""
        self.web_server_manager.update_analysis_results(dataset_id, results)
    
    def get_dashboard_info(self) -> Dict[str, Any]:
        """Get dashboard information."""
        return {
            "web_engine_available": WEB_ENGINE_AVAILABLE,
            "web_server_available": WEB_SERVER_AVAILABLE,
            "server_status": self.server_status,
            "current_port": self.port_spinbox.value(),
            "websocket_port": self.websocket_port_spinbox.value()
        }
