"""
Tab modules for AreTomo3 GUI.
Each tab is implemented as a separate module for better organization.
"""

from .analysis_tab import AnalysisTabManager
from .batch_tab import BatchTabManager
from .export_tab import ExportTabManager
from .log_tab import LogTabManager
from .main_tab import MainTabManager
from .monitor_tab import MonitorTabManager
from .viewer_tab import ViewerTabManager

__all__ = [
    "MainTabManager",
    "AnalysisTabManager",
    "BatchTabManager",
    "MonitorTabManager",
    "ViewerTabManager",
    "ExportTabManager",
    "LogTabManager",
]
