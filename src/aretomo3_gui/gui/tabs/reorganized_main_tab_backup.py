#!/usr/bin/env python3
"""
Reorganized Main Tab - Control Center with Project Management Integration.
Eliminates redundancy and provides unified workflow control.
"""

import json
import logging
import os
from pathlib import Path
from typing import Any, Dict, Optional

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from PyQt6.QtWidgets import (
    QApplication,
    QCheckBox,
    QComboBox,
    QDoubleSpinBox,
    QFileDialog,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QMessageBox,
    QProgressBar,
    QPushButton,
    QScrollArea,
    QSpinBox,
    QSplitter,
    QTabWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

logger = logging.getLogger(__name__)


class ProjectManager:
    """Simple project management for AreTomo3 sessions."""

    def __init__(self):
        """Initialize the instance."""
        self.current_project = None
        self.project_dir = None
        self.auto_save = True

    def create_new_project(self, name: str, base_dir: str) -> bool:
        """Create a new project."""
        try:
            project_path = os.path.join(base_dir, name)
            os.makedirs(project_path, exist_ok=True)

            # Create project structure
            os.makedirs(os.path.join(project_path, "input"), exist_ok=True)
            os.makedirs(os.path.join(project_path, "output"), exist_ok=True)
            os.makedirs(os.path.join(project_path, "analysis"), exist_ok=True)
            os.makedirs(os.path.join(project_path, "sessions"), exist_ok=True)

            self.current_project = name
            self.project_dir = project_path

            logger.info(f"Created new project: {name} at {project_path}")
            return True

        except Exception as e:
            logger.error(f"Error creating project {name}: {e}")
            return False

    def load_project(self, project_path: str) -> bool:
        """Load an existing project."""
        try:
            if os.path.exists(project_path):
                self.current_project = os.path.basename(project_path)
                self.project_dir = project_path
                logger.info(f"Loaded project: {self.current_project}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error loading project {project_path}: {e}")
            return False

    def get_project_paths(self) -> Dict[str, str]:
        """Get standard project paths."""
        if not self.project_dir:
            return {}

        return {
            "input": os.path.join(self.project_dir, "input"),
            "output": os.path.join(self.project_dir, "output"),
            "analysis": os.path.join(self.project_dir, "analysis"),
            "sessions": os.path.join(self.project_dir, "sessions"),
        }

    # TODO: Refactor class - Class 'ReorganizedMainTab' too long (1387 lines)


class ReorganizedMainTab(QWidget):
    """Reorganized main tab serving as control center."""

    # Signals for communication with other components
    processing_mode_changed = pyqtSignal(str)  # single, live, batch
    project_changed = pyqtSignal(str)  # project path
    start_processing = pyqtSignal(dict)  # processing parameters

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.main_window = parent
        self.project_manager = ProjectManager()
        self.setup_ui()
        self.connect_signals()
        logger.info("Reorganized main tab initialized")

    def setup_ui(self):
        """Set up the user interface with sub-tabs in lower half."""
        # Create main vertical splitter
        main_splitter = QSplitter(Qt.Orientation.Vertical)

        # Upper section - Control Center (70% of space)
        self.control_section = self.create_control_section()
        main_splitter.addWidget(self.control_section)

        # Lower section - Sub-tabs (30% of space)
        self.sub_tabs = QTabWidget()
        self.sub_tabs.setMaximumHeight(300)  # Limit height to keep it compact

        # Create sub-tabs (removed analysis - moved to main tab)
        self.config_tab = self.create_config_tab()
        self.command_tab = self.create_command_tab()

        # Add sub-tabs
        self.sub_tabs.addTab(self.config_tab, "⚙️ Configuration")
        self.sub_tabs.addTab(self.command_tab, "📋 Command Preview")

        main_splitter.addWidget(self.sub_tabs)

        # Set splitter proportions (70% upper, 30% lower)
        main_splitter.setStretchFactor(0, 7)
        main_splitter.setStretchFactor(1, 3)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(main_splitter)

    def create_control_section(self):
        """Create the main control center section."""
        # Create scroll area for main content
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)
        layout.setSpacing(15)

        # Welcome header
        self.create_welcome_section(layout)

        # Project management section
        self.create_project_section(layout)

        # Quick setup section
        self.create_quick_setup_section(layout)

        # Essential settings section
        self.create_essential_settings_section(layout)

        # Quick actions section
        self.create_quick_actions_section(layout)

        # Status overview section
        self.create_status_section(layout)

        layout.addStretch()
        scroll.setWidget(scroll_widget)

        return scroll

    # TODO: Refactor function - Function 'create_config_tab' too long (80
    # lines)
    def create_config_tab(self):
        """Create the configuration management tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)

        # Configuration Management Section
        config_group = QGroupBox("⚙️ Configuration Management")
        config_layout = QVBoxLayout(config_group)

        # Template selection row
        template_row = QHBoxLayout()
        template_row.addWidget(QLabel("Template:"))

        self.template_combo = QComboBox()
        self.template_combo.addItems(
            [
                "Custom Configuration",
                "High Resolution Template",
                "Fast Processing Template",
                "Live Acquisition Template",
                "Batch Processing Template",
            ]
        )
        self.template_combo.setMinimumWidth(200)
        self.template_combo.currentTextChanged.connect(self.load_template)
        self.template_combo.setToolTip("Load predefined parameter templates")
        template_row.addWidget(self.template_combo)
        template_row.addStretch()

        config_layout.addLayout(template_row)

        # Configuration buttons row
        buttons_row = QHBoxLayout()

        self.load_json_btn = QPushButton("📂 Load Configuration")
        self.load_json_btn.clicked.connect(self.load_json_config)
        self.load_json_btn.setToolTip("Load parameters from JSON file")
        buttons_row.addWidget(self.load_json_btn)

        self.save_json_btn = QPushButton("💾 Save Configuration")
        self.save_json_btn.clicked.connect(self.save_json_config)
        self.save_json_btn.setToolTip("Save current parameters to JSON file")
        buttons_row.addWidget(self.save_json_btn)

        self.validate_btn = QPushButton("✅ Validate Configuration")
        self.validate_btn.clicked.connect(self.validate_configuration)
        self.validate_btn.setToolTip("Validate current parameter configuration")
        buttons_row.addWidget(self.validate_btn)

        buttons_row.addStretch()
        config_layout.addLayout(buttons_row)

        # Configuration status
        self.config_status = QLabel("📝 Configuration Status: Ready")
        self.config_status.setStyleSheet(
            "font-weight: bold; color: #2c3e50; padding: 10px;"
        )
        config_layout.addWidget(self.config_status)

        layout.addWidget(config_group)

        # Recent configurations
        recent_group = QGroupBox("📚 Recent Configurations")
        recent_layout = QVBoxLayout(recent_group)

        self.recent_configs = QComboBox()
        self.recent_configs.addItems(
            ["config_2024_01_15.json", "high_res_setup.json", "live_processing.json"]
        )
        recent_layout.addWidget(self.recent_configs)

        load_recent_btn = QPushButton("📂 Load Selected")
        load_recent_btn.clicked.connect(self.load_recent_config)
        recent_layout.addWidget(load_recent_btn)

        layout.addWidget(recent_group)
        layout.addStretch()

        return tab

    # TODO: Refactor function - Function 'create_command_tab' too long (95
    # lines)
    def create_command_tab(self):
        """Create the command preview tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)

        # Command Preview Section
        preview_group = QGroupBox("📋 AreTomo3 Command Preview")
        preview_layout = QVBoxLayout(preview_group)

        # Command text area
        self.command_preview = QTextEdit()
        self.command_preview.setReadOnly(True)
        self.command_preview.setMinimumHeight(200)
        self.command_preview.setStyleSheet(
            """
            QTextEdit {
                background-color: #2d3748;
                color: #e2e8f0;
                font-family: 'Courier New', monospace;
                font-size: 10pt;
                border: 1px solid #4a5568;
                border-radius: 5px;
                padding: 10px;
            }
        """
        )
        preview_layout.addWidget(self.command_preview)

        # Command actions
        actions_row = QHBoxLayout()

        self.update_preview_btn = QPushButton("🔄 Update Preview")
        self.update_preview_btn.clicked.connect(self.update_command_preview)
        self.update_preview_btn.setToolTip(
            "Update command preview with current parameters"
        )
        actions_row.addWidget(self.update_preview_btn)

        self.copy_command_btn = QPushButton("📋 Copy Command")
        self.copy_command_btn.clicked.connect(self.copy_command)
        self.copy_command_btn.setToolTip("Copy command to clipboard")
        actions_row.addWidget(self.copy_command_btn)

        self.save_command_btn = QPushButton("💾 Save Command")
        self.save_command_btn.clicked.connect(self.save_command)
        self.save_command_btn.setToolTip("Save command to file")
        actions_row.addWidget(self.save_command_btn)

        self.execute_btn = QPushButton("▶️ Execute Command")
        self.execute_btn.clicked.connect(self.execute_command)
        self.execute_btn.setToolTip("Execute AreTomo3 command")
        self.execute_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """
        )
        actions_row.addWidget(self.execute_btn)

        actions_row.addStretch()
        preview_layout.addLayout(actions_row)

        layout.addWidget(preview_group)

        # Command history
        history_group = QGroupBox("📜 Command History")
        history_layout = QVBoxLayout(history_group)

        self.command_history = QComboBox()
        self.command_history.addItems(
            [
                "AreTomo3 -InPrefix data/tomo1 -OutDir output/",
                "AreTomo3 -InPrefix data/tomo2 -VolZ 2048 -AtBin 2",
                "AreTomo3 -InPrefix data/batch -OutDir results/ -Gpu 0,1,2,3",
            ]
        )
        history_layout.addWidget(self.command_history)

        load_history_btn = QPushButton("📂 Load from History")
        load_history_btn.clicked.connect(self.load_from_history)
        history_layout.addWidget(load_history_btn)

        layout.addWidget(history_group)
        layout.addStretch()

        return tab

    def create_welcome_section(self, layout):
        """Create welcome section."""
        welcome_label = QLabel("🚀 AreTomo3 Control Center")
        welcome_label.setStyleSheet(
            """
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin: 5px;
            }
        """
        )
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(welcome_label)

    def create_project_section(self, layout):
        """Create project management section."""
        project_group = QGroupBox("📁 Project Management")
        project_layout = QGridLayout(project_group)

        # Current project
        project_layout.addWidget(QLabel("Current Project:"), 0, 0)
        self.current_project_combo = QComboBox()
        self.current_project_combo.setEditable(True)
        self.current_project_combo.setPlaceholderText("No project selected")
        project_layout.addWidget(self.current_project_combo, 0, 1, 1, 2)

        # Project actions
        self.new_project_btn = QPushButton("🆕 New")
        self.load_project_btn = QPushButton("📂 Load")
        self.save_project_btn = QPushButton("💾 Save")

        project_layout.addWidget(self.new_project_btn, 0, 3)
        project_layout.addWidget(self.load_project_btn, 0, 4)
        project_layout.addWidget(self.save_project_btn, 0, 5)

        # Session management
        project_layout.addWidget(QLabel("Session:"), 1, 0)
        self.session_combo = QComboBox()
        self.session_combo.addItems(["Session_001", "Session_002", "Session_003"])
        project_layout.addWidget(self.session_combo, 1, 1)

        self.auto_save_chk = QCheckBox("Auto-save session")
        self.auto_save_chk.setChecked(True)
        project_layout.addWidget(self.auto_save_chk, 1, 2, 1, 2)

        layout.addWidget(project_group)

    def create_quick_setup_section(self, layout):
        """Create quick setup section."""
        setup_group = QGroupBox("⚡ Quick Setup")
        setup_layout = QGridLayout(setup_group)

        # AreTomo3 path
        setup_layout.addWidget(QLabel("AreTomo3 Path:"), 0, 0)
        self.aretomo_path = QLineEdit()
        self.aretomo_path.setText(
            os.environ.get("ARETOMO3_PATH", "/usr/local/bin/AreTomo3")
        )
        setup_layout.addWidget(self.aretomo_path, 0, 1, 1, 2)

        self.browse_aretomo_btn = QPushButton("📁")
        self.browse_aretomo_btn.setMaximumWidth(40)
        setup_layout.addWidget(self.browse_aretomo_btn, 0, 3)

        self.test_aretomo_btn = QPushButton("🧪 Test")
        setup_layout.addWidget(self.test_aretomo_btn, 0, 4)

        # Input directory
        setup_layout.addWidget(QLabel("Input Directory:"), 1, 0)
        self.input_dir = QLineEdit()
        setup_layout.addWidget(self.input_dir, 1, 1, 1, 2)

        self.browse_input_btn = QPushButton("📁")
        self.browse_input_btn.setMaximumWidth(40)
        setup_layout.addWidget(self.browse_input_btn, 1, 3)

        self.scan_input_btn = QPushButton("🔍 Scan")
        setup_layout.addWidget(self.scan_input_btn, 1, 4)

        # Output directory
        setup_layout.addWidget(QLabel("Output Directory:"), 2, 0)
        self.output_dir = QLineEdit()
        setup_layout.addWidget(self.output_dir, 2, 1, 1, 2)

        self.browse_output_btn = QPushButton("📁")
        self.browse_output_btn.setMaximumWidth(40)
        setup_layout.addWidget(self.browse_output_btn, 2, 3)

        self.create_output_btn = QPushButton("📂 Create")
        setup_layout.addWidget(self.create_output_btn, 2, 4)

        layout.addWidget(setup_group)

    def create_essential_settings_section(self, layout):
        """Create essential settings section."""
        settings_group = QGroupBox("🔧 Essential Settings")
        settings_layout = QGridLayout(settings_group)

        # Left column - Microscope essentials
        settings_layout.addWidget(QLabel("Pixel Size:"), 0, 0)
        self.pixel_size = QDoubleSpinBox()
        self.pixel_size.setRange(0.1, 100.0)
        self.pixel_size.setValue(1.91)
        self.pixel_size.setSuffix(" Å")
        self.pixel_size.setDecimals(3)
        settings_layout.addWidget(self.pixel_size, 0, 1)

        settings_layout.addWidget(QLabel("Voltage:"), 1, 0)
        self.voltage = QSpinBox()
        self.voltage.setRange(60, 300)
        self.voltage.setValue(300)
        self.voltage.setSuffix(" kV")
        settings_layout.addWidget(self.voltage, 1, 1)

        settings_layout.addWidget(QLabel("Tilt Axis:"), 2, 0)
        self.tilt_axis = QDoubleSpinBox()
        self.tilt_axis.setRange(-180.0, 180.0)
        self.tilt_axis.setValue(-95.75)
        self.tilt_axis.setSuffix("°")
        self.tilt_axis.setDecimals(2)
        settings_layout.addWidget(self.tilt_axis, 2, 1)

        # Right column - Processing mode and GPU
        settings_layout.addWidget(QLabel("GPU Selection:"), 0, 2)
        self.gpu_selection = QLineEdit()
        self.gpu_selection.setText("0,1,2,3")
        self.gpu_selection.setPlaceholderText("e.g., 0,1,2,3")
        settings_layout.addWidget(self.gpu_selection, 0, 3)

        settings_layout.addWidget(QLabel("Processing Mode:"), 1, 2)
        self.processing_mode = QComboBox()
        self.processing_mode.addItems(
            ["🔵 Single Series", "🔴 Live Processing", "📦 Batch Processing"]
        )
        settings_layout.addWidget(self.processing_mode, 1, 3)

        layout.addWidget(settings_group)

    # TODO: Refactor function - Function 'create_quick_actions_section' too
    # long (89 lines)
    def create_quick_actions_section(self, layout):
        """Create quick actions section."""
        actions_group = QGroupBox("🎮 Quick Actions")
        actions_layout = QHBoxLayout(actions_group)
        actions_layout.setSpacing(15)

        # Main action buttons
        self.load_data_btn = QPushButton("🔍 Load Data")
        self.load_data_btn.setMinimumHeight(50)
        self.load_data_btn.setStyleSheet(
            """
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #3498db;
                color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """
        )

        self.configure_btn = QPushButton("⚙️ Configure")
        self.configure_btn.setMinimumHeight(50)
        self.configure_btn.setStyleSheet(
            """
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #f39c12;
                color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """
        )

        self.start_btn = QPushButton("▶️ Start")
        self.start_btn.setMinimumHeight(50)
        self.start_btn.setStyleSheet(
            """
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #27ae60;
                color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """
        )

        self.stop_btn = QPushButton("⏹️ Stop")
        self.stop_btn.setMinimumHeight(50)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet(
            """
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #e74c3c;
                color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """
        )

        actions_layout.addWidget(self.load_data_btn)
        actions_layout.addWidget(self.configure_btn)
        actions_layout.addWidget(self.start_btn)
        actions_layout.addWidget(self.stop_btn)

        layout.addWidget(actions_group)

    def create_status_section(self, layout):
        """Create status overview section."""
        status_group = QGroupBox("📊 Status Overview")
        status_layout = QGridLayout(status_group)

        # Current status
        status_layout.addWidget(QLabel("Current Status:"), 0, 0)
        self.status_label = QLabel("🟡 Ready")
        self.status_label.setStyleSheet("font-weight: bold; color: #f39c12;")
        status_layout.addWidget(self.status_label, 0, 1)

        # Queue info
        status_layout.addWidget(QLabel("Queue:"), 0, 2)
        self.queue_label = QLabel("0 jobs")
        status_layout.addWidget(self.queue_label, 0, 3)

        # Progress bar
        status_layout.addWidget(QLabel("Progress:"), 1, 0)
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimumHeight(25)
        status_layout.addWidget(self.progress_bar, 1, 1, 1, 2)

        # ETA
        status_layout.addWidget(QLabel("ETA:"), 1, 3)
        self.eta_label = QLabel("--:--:--")
        status_layout.addWidget(self.eta_label, 1, 4)

        # Last result
        status_layout.addWidget(QLabel("Last Result:"), 2, 0)
        self.last_result_label = QLabel("None")
        status_layout.addWidget(self.last_result_label, 2, 1, 1, 4)

        layout.addWidget(status_group)

    def connect_signals(self):
        """Connect UI signals."""
        # Project management
        self.new_project_btn.clicked.connect(self.create_new_project)
        self.load_project_btn.clicked.connect(self.load_project)
        self.save_project_btn.clicked.connect(self.save_project)

        # File browsing
        self.browse_aretomo_btn.clicked.connect(self.browse_aretomo_path)
        self.browse_input_btn.clicked.connect(self.browse_input_directory)
        self.browse_output_btn.clicked.connect(self.browse_output_directory)

        # Actions
        self.load_data_btn.clicked.connect(self.load_data)
        self.configure_btn.clicked.connect(self.configure_parameters)
        self.start_btn.clicked.connect(self.start_processing_action)
        self.stop_btn.clicked.connect(self.stop_processing_action)

        # Processing mode
        self.processing_mode.currentTextChanged.connect(self.on_processing_mode_changed)

    # Project management methods
    def create_new_project(self):
        """Create a new project."""
        from PyQt6.QtWidgets import QInputDialog

        name, ok = QInputDialog.getText(self, "New Project", "Project name:")
        if ok and name:
            base_dir = QFileDialog.getExistingDirectory(self, "Select Project Location")
            if base_dir:
                if self.project_manager.create_new_project(name, base_dir):
                    self.current_project_combo.setCurrentText(name)
                    self.update_project_paths()
                    QMessageBox.information(
                        self, "Success", f"Project '{name}' created successfully!"
                    )
                else:
                    QMessageBox.warning(
                        self, "Error", f"Failed to create project '{name}'"
                    )

    def load_project(self):
        """Load an existing project."""
        project_dir = QFileDialog.getExistingDirectory(self, "Select Project Directory")
        if project_dir:
            if self.project_manager.load_project(project_dir):
                self.current_project_combo.setCurrentText(
                    self.project_manager.current_project
                )
                self.update_project_paths()
                QMessageBox.information(
                    self, "Success", f"Project loaded successfully!"
                )
            else:
                QMessageBox.warning(self, "Error", "Failed to load project")

    def save_project(self):
        """Save current project settings."""
        if self.project_manager.current_project:
            # Save current settings to project
            QMessageBox.information(self, "Success", "Project settings saved!")
        else:
            QMessageBox.warning(self, "No Project", "No project is currently loaded")

    def update_project_paths(self):
        """Update input/output paths based on current project."""
        if self.project_manager.current_project:
            paths = self.project_manager.get_project_paths()
            self.input_dir.setText(paths.get("input", ""))
            self.output_dir.setText(paths.get("output", ""))
            self.project_changed.emit(self.project_manager.project_dir)

    # File browsing methods
    def browse_aretomo_path(self):
        """Browse for AreTomo3 executable."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select AreTomo3 Executable", "", "Executable Files (*)"
        )
        if file_path:
            self.aretomo_path.setText(file_path)

    def browse_input_directory(self):
        """Browse for input directory."""
        directory = QFileDialog.getExistingDirectory(self, "Select Input Directory")
        if directory:
            self.input_dir.setText(directory)

    def browse_output_directory(self):
        """Browse for output directory."""
        directory = QFileDialog.getExistingDirectory(self, "Select Output Directory")
        if directory:
            self.output_dir.setText(directory)

    # Action methods
    def load_data(self):
        """Load data for processing."""
        input_dir = self.input_dir.text().strip()
        if not input_dir or not os.path.exists(input_dir):
            QMessageBox.warning(
                self, "Invalid Input", "Please select a valid input directory"
            )
            return

        # Scan for data files
        data_files = []
        for ext in ["*.eer", "*.tif", "*.tiff", "*.mrc", "*.mrcs"]:
            data_files.extend(Path(input_dir).rglob(ext))

        if data_files:
            QMessageBox.information(
                self, "Data Found", f"Found {len(data_files)} data files"
            )
            self.status_label.setText("🟢 Data loaded")
            self.status_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        else:
            QMessageBox.warning(self, "No Data", "No compatible data files found")

    def configure_parameters(self):
        """Open parameters configuration."""
        if self.main_window:
            # Switch to parameters tab
            for i in range(self.main_window.tabs.count()):
                if "Parameters" in self.main_window.tabs.tabText(i):
                    self.main_window.tabs.setCurrentIndex(i)
                    break

    def start_processing_action(self):
        """Start processing with current settings."""
        # Validate settings
        if not self.input_dir.text().strip():
            QMessageBox.warning(
                self, "Missing Input", "Please select an input directory"
            )
            return

        if not self.output_dir.text().strip():
            QMessageBox.warning(
                self, "Missing Output", "Please select an output directory"
            )
            return

        # Gather processing parameters
        params = {
            "input_dir": self.input_dir.text().strip(),
            "output_dir": self.output_dir.text().strip(),
            "aretomo_path": self.aretomo_path.text().strip(),
            "pixel_size": self.pixel_size.value(),
            "voltage": self.voltage.value(),
            "tilt_axis": self.tilt_axis.value(),
            "gpu_selection": self.gpu_selection.text().strip(),
            "processing_mode": self.processing_mode.currentText(),
        }

        # Update UI state
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.status_label.setText("🔄 Processing...")
        self.status_label.setStyleSheet("font-weight: bold; color: #3498db;")

        # Emit signal to start processing
        self.start_processing.emit(params)

        logger.info(f"Started processing with mode: { params['processing_mode']}")

    def stop_processing_action(self):
        """Stop current processing."""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("🟡 Ready")
        self.status_label.setStyleSheet("font-weight: bold; color: #f39c12;")
        self.progress_bar.setValue(0)

        logger.info("Processing stopped by user")

    def on_processing_mode_changed(self, mode_text: str):
        """Handle processing mode change."""
        mode = mode_text.split()[1].lower()  # Extract mode from "🔴 Live Processing"
        self.processing_mode_changed.emit(mode)
        logger.info(f"Processing mode changed to: {mode}")

    # Status update methods
    def update_status(self, status: str, color: str = "#f39c12"):
        """Update status display."""
        self.status_label.setText(status)
        self.status_label.setStyleSheet(f"font-weight: bold; color: {color};")

    def update_progress(self, value: int, eta: str = ""):
        """Update progress bar and ETA."""
        self.progress_bar.setValue(value)
        if eta:
            self.eta_label.setText(eta)

    def update_queue_info(self, queue_size: int):
        """Update queue information."""
        self.queue_label.setText(f"{queue_size} jobs")

    def update_last_result(self, result_path: str):
        """Update last result information."""
        if result_path:
            result_name = os.path.basename(result_path)
            self.last_result_label.setText(result_name)

    def get_current_settings(self) -> Dict[str, Any]:
        """Get current settings as dictionary."""
        return {
            "project": self.project_manager.current_project,
            "input_dir": self.input_dir.text().strip(),
            "output_dir": self.output_dir.text().strip(),
            "aretomo_path": self.aretomo_path.text().strip(),
            "pixel_size": self.pixel_size.value(),
            "voltage": self.voltage.value(),
            "tilt_axis": self.tilt_axis.value(),
            "gpu_selection": self.gpu_selection.text().strip(),
            "processing_mode": self.processing_mode.currentText(),
            "auto_save": self.auto_save_chk.isChecked(),
        }

    # TODO: Refactor set_settings - complexity: 12 (target: <10)
    def set_settings(self, settings: Dict[str, Any]):
        """Set settings from dictionary."""
        if "input_dir" in settings:
            self.input_dir.setText(settings["input_dir"])
        if "output_dir" in settings:
            self.output_dir.setText(settings["output_dir"])
        if "aretomo_path" in settings:
            self.aretomo_path.setText(settings["aretomo_path"])
        if "pixel_size" in settings:
            self.pixel_size.setValue(settings["pixel_size"])
        if "voltage" in settings:
            self.voltage.setValue(settings["voltage"])
        if "tilt_axis" in settings:
            self.tilt_axis.setValue(settings["tilt_axis"])
        if "gpu_selection" in settings:
            self.gpu_selection.setText(settings["gpu_selection"])
        if "processing_mode" in settings:
            # Find and set the processing mode
            for i in range(self.processing_mode.count()):
                if settings["processing_mode"] in self.processing_mode.itemText(i):
                    self.processing_mode.setCurrentIndex(i)
                    break
        if "auto_save" in settings:
            self.auto_save_chk.setChecked(settings["auto_save"])

    # Configuration management methods
    # TODO: Refactor function - Function 'load_template' too long (58 lines)
    def load_template(self, template_name):
        """Load predefined parameter template."""
        if template_name == "Custom Configuration":
            return

        templates = {
            "High Resolution Template": {
                "pixel_size": 1.91,
                "voltage": 300,
                "tilt_axis": -95.75,
                "gpu_selection": "0,1",
                "processing_mode": "🔵 Single Series",
            },
            "Fast Processing Template": {
                "pixel_size": 1.91,
                "voltage": 300,
                "tilt_axis": -95.75,
                "gpu_selection": "0",
                "processing_mode": "🔵 Single Series",
            },
            "Live Acquisition Template": {
                "pixel_size": 1.91,
                "voltage": 300,
                "tilt_axis": -95.75,
                "gpu_selection": "0,1,2",
                "processing_mode": "🔴 Live Processing",
            },
            "Batch Processing Template": {
                "pixel_size": 1.91,
                "voltage": 300,
                "tilt_axis": -95.75,
                "gpu_selection": "0,1,2,3",
                "processing_mode": "📦 Batch Processing",
            },
        }

        if template_name in templates:
            template = templates[template_name]
            self.pixel_size.setValue(template["pixel_size"])
            self.voltage.setValue(template["voltage"])
            self.tilt_axis.setValue(template["tilt_axis"])
            self.gpu_selection.setText(template["gpu_selection"])

            # Set processing mode
            for i in range(self.processing_mode.count()):
                if template["processing_mode"] in self.processing_mode.itemText(i):
                    self.processing_mode.setCurrentIndex(i)
                    break

            self.config_status.setText(
                f"📝 Configuration Status: {template_name} loaded"
            )
            self.config_status.setStyleSheet(
                "font-weight: bold; color: #27ae60; padding: 10px;"
            )
            QMessageBox.information(
                self, "Template Loaded", f"Applied {template_name} settings"
            )

    def load_json_config(self):
        """Load parameters from JSON configuration file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load JSON Configuration", "", "JSON files (*.json);;All files (*.*)"
        )

        if file_path:
            try:
                with open(file_path) as f:
                    config = json.load(f)

                # Apply configuration
                if "settings" in config:
                    self.set_settings(config["settings"])
                elif "aretomo3_config" in config:
                    self.set_settings(config["aretomo3_config"].get("settings", {}))

                self.config_status.setText(
                    f"📝 Configuration Status: { os.path.basename(file_path)} loaded"
                )
                self.config_status.setStyleSheet(
                    "font-weight: bold; color: #27ae60; padding: 10px;"
                )
                QMessageBox.information(
                    self,
                    "Success",
                    f"Configuration loaded from {os.path.basename(file_path)}",
                )

            except Exception as e:
                QMessageBox.critical(
                    self, "Error", f"Failed to load configuration: {str(e)}"
                )

    def save_json_config(self):
        """Save current parameters to JSON configuration file."""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save JSON Configuration",
            "aretomo3_config.json",
            "JSON files (*.json);;All files (*.*)",
        )

        if file_path:
            try:
                config = {
                    "aretomo3_config": {
                        "version": "1.0",
                        "settings": self.get_current_settings(),
                        "metadata": {
                            "created_by": "AreTomo3 GUI",
                            "timestamp": str(
                                os.path.getmtime(__file__)
                                if os.path.exists(__file__)
                                else "unknown"
                            ),
                        },
                    }
                }

                with open(file_path, "w") as f:
                    json.dump(config, f, indent=2)

                self.config_status.setText(
                    f"📝 Configuration Status: { os.path.basename(file_path)} saved"
                )
                self.config_status.setStyleSheet(
                    "font-weight: bold; color: #27ae60; padding: 10px;"
                )
                QMessageBox.information(
                    self,
                    "Success",
                    f"Configuration saved to {os.path.basename(file_path)}",
                )

            except Exception as e:
                QMessageBox.critical(
                    self, "Error", f"Failed to save configuration: {str(e)}"
                )

    # TODO: Refactor validate_configuration - complexity: 11 (target: <10)

    # TODO: Refactor function - Function 'validate_configuration' too long (53
    # lines)
    def validate_configuration(self):
        """Validate current parameter configuration."""
        issues = []

        # Check required paths
        if not self.input_dir.text().strip():
            issues.append("Input directory is required")

        if not self.output_dir.text().strip():
            issues.append("Output directory is required")

        if not self.aretomo_path.text().strip():
            issues.append("AreTomo3 executable path is required")

        # Check parameter ranges
        if self.pixel_size.value() <= 0:
            issues.append("Pixel size must be greater than 0")

        if self.voltage.value() < 60 or self.voltage.value() > 300:
            issues.append("Voltage should be between 60-300 kV")

        # Check GPU configuration
        gpu_text = self.gpu_selection.text().strip()
        if gpu_text:
            try:
                gpu_ids = [int(x.strip()) for x in gpu_text.split(",")]
                if any(gpu_id < 0 for gpu_id in gpu_ids):
                    issues.append("GPU IDs must be non-negative")
            except ValueError:
                issues.append("Invalid GPU ID format (use comma-separated numbers)")

        # Show validation results
        if issues:
            self.config_status.setText("📝 Configuration Status: Issues found")
            self.config_status.setStyleSheet(
                "font-weight: bold; color: #e74c3c; padding: 10px;"
            )
            QMessageBox.warning(
                self,
                "Configuration Issues",
                "Found the following issues:\n\n"
                + "\n".join(f"• {issue}" for issue in issues),
            )
        else:
            self.config_status.setText("📝 Configuration Status: Valid ✅")
            self.config_status.setStyleSheet(
                "font-weight: bold; color: #27ae60; padding: 10px;"
            )
            QMessageBox.information(
                self,
                "Validation Passed",
                "✅ Configuration is valid and ready for processing!",
            )

    def load_recent_config(self):
        """Load selected recent configuration."""
        selected = self.recent_configs.currentText()
        QMessageBox.information(self, "Load Recent", f"Loading {selected}...")
        # TODO: Implement actual loading logic

    # Command preview methods
    def update_command_preview(self):
        """Update command preview with current settings."""
        try:
            # Build AreTomo3 command
            cmd_parts = [self.aretomo_path.text().strip() or "AreTomo3"]

            # Add input/output
            if self.input_dir.text().strip():
                cmd_parts.append(f"-InPrefix {self.input_dir.text().strip()}")

            if self.output_dir.text().strip():
                cmd_parts.append(f"-OutDir {self.output_dir.text().strip()}")

            # Add essential parameters
            cmd_parts.append(f"-PixSize {self.pixel_size.value()}")
            cmd_parts.append(f"-Kv {self.voltage.value()}")
            cmd_parts.append(f"-TiltAxis {self.tilt_axis.value()}")

            # Add GPU selection
            if self.gpu_selection.text().strip():
                cmd_parts.append(f"-Gpu {self.gpu_selection.text().strip()}")

            # Join command parts
            command = " ".join(cmd_parts)

            # Update preview
            self.command_preview.setPlainText(command)

        except Exception as e:
            self.command_preview.setPlainText(f"Error generating command: {str(e)}")

    def copy_command(self):
        """Copy the command to clipboard."""
        try:
            command = self.command_preview.toPlainText()
            if command:
                QApplication.clipboard().setText(command)
                QMessageBox.information(self, "Success", "Command copied to clipboard!")
            else:
                QMessageBox.warning(
                    self,
                    "No Command",
                    "No command to copy. Please update preview first.",
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to copy command: { str(e)}",
            )

    def save_command(self):
        """Save command to file."""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Command",
            "aretomo3_command.sh",
            "Shell scripts (*.sh);;Text files (*.txt);;All files (*.*)",
        )

        if file_path:
            try:
                command = self.command_preview.toPlainText()
                with open(file_path, "w") as f:
                    f.write("#!/bin/bash\n")
                    f.write(f"# AreTomo3 command generated by GUI\n")
                    f.write(
                        f"# Generated on: { os.path.getmtime(__file__) if os.path.exists(__file__) else 'unknown'}\n\n"
                    )
                    f.write(command + "\n")

                # Make executable on Unix systems
                if os.name != "nt":
                    os.chmod(file_path, 0o755)

                QMessageBox.information(
                    self,
                    "Success",
                    f"Command saved to { os.path.basename(file_path)}",
                )

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to save command: { str(e)}",
                )

    def execute_command(self):
        """Execute AreTomo3 command."""
        command = self.command_preview.toPlainText()
        if not command:
            QMessageBox.warning(
                self,
                "No Command",
                "No command to execute. Please update preview first.",
            )
            return

        reply = QMessageBox.question(
            self,
            "Execute Command",
            f"Are you sure you want to execute this command?\n\n{command}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            # TODO: Implement actual command execution
            QMessageBox.information(
                self, "Execute", "Command execution would start here..."
            )
            logger.info(f"Would execute: {command}")

    def load_from_history(self):
        """Load command from history."""
        selected = self.command_history.currentText()
        if selected:
            self.command_preview.setPlainText(selected)
            QMessageBox.information(
                self, "History Loaded", f"Command loaded from history"
            )

    # Analysis methods
    # TODO: Refactor function - Function 'auto_detect_results' too long (66
    # lines)
    def auto_detect_results(self):
        """Automatically detect results directory based on command generation."""
        try:
            # Get output directory from current settings
            output_dir = self.output_dir.text().strip()
            if not output_dir:
                QMessageBox.warning(
                    self, "No Output Directory", "Please set an output directory first."
                )
                return

            # Check if output directory exists
            if not os.path.exists(output_dir):
                QMessageBox.warning(
                    self,
                    "Directory Not Found",
                    f"Output directory does not exist: {output_dir}",
                )
                return

            # Search for AreTomo3 result files
            result_patterns = [
                "*.mrc",  # Reconstructed tomograms
                "*.rec",  # Reconstruction files
                "*.xf",  # Transform files
                "*.tlt",  # Tilt files
                "*.log",  # Log files
                "*_ctf.txt",  # CTF files
                "*_motion.txt",  # Motion correction files
            ]

            found_files = []
            for pattern in result_patterns:
                files = list(Path(output_dir).rglob(pattern))
                found_files.extend(files)

            if found_files:
                self.results_dir_label.setText(output_dir)
                self.analysis_status.setText(
                    f"📝 Status: Found {len(found_files)} result files"
                )
                self.analysis_status.setStyleSheet(
                    "font-weight: bold; color: #27ae60; padding: 5px;"
                )

                # Automatically refresh analysis
                self.refresh_analysis()

                QMessageBox.information(
                    self,
                    "Results Detected",
                    f"Found {len(found_files)} result files in:\n{output_dir}",
                )
            else:
                self.analysis_status.setText("📝 Status: No result files found")
                self.analysis_status.setStyleSheet(
                    "font-weight: bold; color: #e74c3c; padding: 5px;"
                )
                QMessageBox.information(
                    self,
                    "No Results",
                    f"No AreTomo3 result files found in:\n{output_dir}",
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to detect results: { str(e)}",
            )

    def browse_results_directory(self):
        """Manually browse for results directory."""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Results Directory",
            self.output_dir.text().strip() or str(Path.home()),
        )

        if directory:
            self.results_dir_label.setText(directory)
            self.analysis_status.setText("📝 Status: Results directory set manually")
            self.analysis_status.setStyleSheet(
                "font-weight: bold; color: #2c3e50; padding: 5px;"
            )
            self.refresh_analysis()

    def refresh_analysis(self):
        """Refresh analysis plots from current results directory."""
        try:
            results_dir = self.results_dir_label.text()
            if results_dir == "No results directory detected":
                QMessageBox.warning(
                    self,
                    "No Directory",
                    "Please detect or select a results directory first.",
                )
                return

            if not os.path.exists(results_dir):
                QMessageBox.warning(
                    self,
                    "Directory Not Found",
                    f"Results directory does not exist: {results_dir}",
                )
                return

            # Generate analysis plots
            self.generate_analysis_plots(results_dir)

            self.analysis_status.setText("📝 Status: Analysis plots updated")
            self.analysis_status.setStyleSheet(
                "font-weight: bold; color: #27ae60; padding: 5px;"
            )

        except Exception as e:
            self.analysis_status.setText(f"📝 Status: Error - {str(e)}")
            self.analysis_status.setStyleSheet(
                "font-weight: bold; color: #e74c3c; padding: 5px;"
            )
            QMessageBox.critical(
                self, "Analysis Error", f"Failed to refresh analysis: {str(e)}"
            )

    # TODO: Refactor function - Function 'generate_analysis_plots' too long
    # (149 lines)
    def generate_analysis_plots(self, results_dir):
        """Generate analysis plots from AreTomo3 results."""
        try:

            import matplotlib.pyplot as plt
            import numpy as np

            # Clear previous plots
            self.analysis_figure.clear()

            # Create subplots
            axes = self.analysis_figure.subplots(2, 2, figsize=(12, 8))
            self.analysis_figure.suptitle(
                f"AreTomo3 Analysis - {os.path.basename(results_dir)}", fontsize=14
            )

            results_path = Path(results_dir)

            # Plot 1: Tilt angles (if .tlt file exists)
            tlt_files = list(results_path.rglob("*.tlt"))
            if tlt_files:
                tlt_file = tlt_files[0]
                try:
                    tilt_angles = np.loadtxt(tlt_file)
                    axes[0, 0].plot(tilt_angles, "bo-", markersize=4)
                    axes[0, 0].set_title("Tilt Angles")
                    axes[0, 0].set_xlabel("Image Index")
                    axes[0, 0].set_ylabel("Tilt Angle (degrees)")
                    axes[0, 0].grid(True, alpha=0.3)
                except Exception:
                    axes[0, 0].text(
                        0.5,
                        0.5,
                        "Error loading tilt data",
                        ha="center",
                        va="center",
                        transform=axes[0, 0].transAxes,
                    )
            else:
                axes[0, 0].text(
                    0.5,
                    0.5,
                    "No tilt file found",
                    ha="center",
                    va="center",
                    transform=axes[0, 0].transAxes,
                )

            # Plot 2: File count by type
            file_types = {
                "MRC files": len(list(results_path.rglob("*.mrc"))),
                "REC files": len(list(results_path.rglob("*.rec"))),
                "XF files": len(list(results_path.rglob("*.xf"))),
                "LOG files": len(list(results_path.rglob("*.log"))),
            }

            types = list(file_types.keys())
            counts = list(file_types.values())

            if any(counts):
                bars = axes[0, 1].bar(
                    types, counts, color=["#3498db", "#e74c3c", "#f39c12", "#27ae60"]
                )
                axes[0, 1].set_title("Result Files by Type")
                axes[0, 1].set_ylabel("File Count")
                axes[0, 1].tick_params(axis="x", rotation=45)

                # Add value labels on bars
                for bar, count in zip(bars, counts):
                    if count > 0:
                        axes[0, 1].text(
                            bar.get_x() + bar.get_width() / 2,
                            bar.get_height() + 0.1,
                            str(count),
                            ha="center",
                            va="bottom",
                        )
            else:
                axes[0, 1].text(
                    0.5,
                    0.5,
                    "No result files found",
                    ha="center",
                    va="center",
                    transform=axes[0, 1].transAxes,
                )

            # Plot 3: Directory structure
            subdirs = [d for d in results_path.iterdir() if d.is_dir()]
            if subdirs:
                # Limit to 10 subdirs
                subdir_names = [d.name for d in subdirs[:10]]
                subdir_counts = [len(list(d.iterdir())) for d in subdirs[:10]]

                axes[1, 0].barh(subdir_names, subdir_counts, color="#9b59b6")
                axes[1, 0].set_title("Files per Subdirectory")
                axes[1, 0].set_xlabel("File Count")
            else:
                axes[1, 0].text(
                    0.5,
                    0.5,
                    "No subdirectories found",
                    ha="center",
                    va="center",
                    transform=axes[1, 0].transAxes,
                )

            # Plot 4: Processing summary
            total_files = sum(counts)
            mrc_files = file_types["MRC files"]

            # Create a simple summary pie chart
            if total_files > 0:
                summary_data = [mrc_files, total_files - mrc_files]
                summary_labels = ["Tomograms", "Other Files"]
                colors = ["#27ae60", "#95a5a6"]

                axes[1, 1].pie(
                    summary_data,
                    labels=summary_labels,
                    colors=colors,
                    autopct="%1.1f%%",
                    startangle=90,
                )
                axes[1, 1].set_title("Processing Summary")
            else:
                axes[1, 1].text(
                    0.5,
                    0.5,
                    "No data for summary",
                    ha="center",
                    va="center",
                    transform=axes[1, 1].transAxes,
                )

            # Adjust layout and refresh canvas
            self.analysis_figure.tight_layout()
            self.analysis_canvas.draw()

        except ImportError:
            QMessageBox.warning(
                self,
                "Missing Dependencies",
                "Matplotlib is required for analysis plots.",
            )
        except Exception as e:
            QMessageBox.critical(
                self, "Plot Error", f"Failed to generate plots: {str(e)}"
            )

    def save_analysis_plots(self):
        """Save analysis plots as PNG files."""
        try:
            results_dir = self.results_dir_label.text()
            if results_dir == "No results directory detected":
                QMessageBox.warning(
                    self,
                    "No Directory",
                    "Please detect or select a results directory first.",
                )
                return

            # Create plots subdirectory
            plots_dir = Path(results_dir) / "analysis_plots"
            plots_dir.mkdir(exist_ok=True)

            # Save the current figure
            timestamp = str(
                int(os.path.getmtime(__file__) if os.path.exists(__file__) else 0)
            )
            plot_filename = plots_dir / f"aretomo3_analysis_{timestamp}.png"

            self.analysis_figure.savefig(plot_filename, dpi=300, bbox_inches="tight")

            self.analysis_status.setText(f"📝 Status: Plots saved to {plots_dir}")
            self.analysis_status.setStyleSheet(
                "font-weight: bold; color: #27ae60; padding: 5px;"
            )

            QMessageBox.information(
                self, "Plots Saved", f"Analysis plots saved to:\n{plot_filename}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Save Error",
                f"Failed to save plots: { str(e)}",
            )
