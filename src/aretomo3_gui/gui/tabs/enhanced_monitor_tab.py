#!/usr/bin/env python3
"""
Enhanced Monitor Tab for AreTomo3 GUI.
Provides system performance monitoring and configuration display in side-by-side panels.
"""

import logging
import platform
from datetime import datetime
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QGroupBox, 
    QLabel, QProgressBar, QTextEdit, QFormLayout, QGridLayout
)

logger = logging.getLogger(__name__)

# Check for system monitoring capabilities
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logger.warning("psutil not available - system monitoring will be limited")

try:
    import GPUtil
    GPUTIL_AVAILABLE = True
except ImportError:
    GPUTIL_AVAILABLE = False


class EnhancedMonitorTab(QWidget):
    """Enhanced monitor tab with system performance and configuration panels."""
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.monitor_timer = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the enhanced monitor interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)  # Reduced margins
        layout.setSpacing(5)  # Reduced spacing

        # Main splitter for side-by-side layout (no header to save space)
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel - System Performance
        performance_panel = self.create_performance_panel()
        main_splitter.addWidget(performance_panel)

        # Right panel - Configuration
        config_panel = self.create_configuration_panel()
        main_splitter.addWidget(config_panel)

        # Set splitter proportions (50/50)
        main_splitter.setStretchFactor(0, 1)
        main_splitter.setStretchFactor(1, 1)

        layout.addWidget(main_splitter)

        # Start monitoring if psutil is available
        if PSUTIL_AVAILABLE:
            self.start_monitoring()
        
    def create_performance_panel(self):
        """Create the system performance monitoring panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Performance header (smaller)
        perf_header = QLabel("🖥️ System Performance")
        perf_header.setStyleSheet("font-size: 14px; font-weight: bold; padding: 2px;")
        layout.addWidget(perf_header)
        
        if PSUTIL_AVAILABLE:
            # CPU Usage
            cpu_group = QGroupBox("🖥️ CPU Usage")
            cpu_layout = QGridLayout(cpu_group)
            
            self.cpu_label = QLabel("CPU: 0%")
            self.cpu_progress = QProgressBar()
            self.cpu_progress.setRange(0, 100)
            
            cpu_layout.addWidget(self.cpu_label, 0, 0)
            cpu_layout.addWidget(self.cpu_progress, 0, 1)
            layout.addWidget(cpu_group)
            
            # Memory Usage
            memory_group = QGroupBox("💾 Memory Usage")
            memory_layout = QGridLayout(memory_group)
            
            self.memory_label = QLabel("Memory: 0%")
            self.memory_progress = QProgressBar()
            self.memory_progress.setRange(0, 100)
            
            memory_layout.addWidget(self.memory_label, 0, 0)
            memory_layout.addWidget(self.memory_progress, 0, 1)
            layout.addWidget(memory_group)
            
            # Disk Usage
            disk_group = QGroupBox("💿 Disk Usage")
            disk_layout = QGridLayout(disk_group)
            
            self.disk_label = QLabel("Disk: 0%")
            self.disk_progress = QProgressBar()
            self.disk_progress.setRange(0, 100)
            
            disk_layout.addWidget(self.disk_label, 0, 0)
            disk_layout.addWidget(self.disk_progress, 0, 1)
            layout.addWidget(disk_group)
            
            # GPU Usage
            gpu_group = QGroupBox("🎮 GPU Usage")
            gpu_layout = QVBoxLayout(gpu_group)
            self.gpu_label = QLabel("GPU: Checking...")
            gpu_layout.addWidget(self.gpu_label)
            layout.addWidget(gpu_group)
            
        else:
            # Fallback when psutil not available
            no_psutil_label = QLabel(
                "⚠️ System monitoring not available\n\n"
                "Install psutil for system monitoring:\n"
                "pip install psutil"
            )
            no_psutil_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_psutil_label.setStyleSheet("color: #888; padding: 20px;")
            layout.addWidget(no_psutil_label)
        
        layout.addStretch()
        return panel
        
    def create_configuration_panel(self):
        """Create the configuration monitoring panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Configuration header (smaller)
        config_header = QLabel("⚙️ Configuration")
        config_header.setStyleSheet("font-size: 14px; font-weight: bold; padding: 2px;")
        layout.addWidget(config_header)
        
        # System Information
        system_group = QGroupBox("🖥️ System Information")
        system_layout = QFormLayout(system_group)
        
        system_layout.addRow("OS:", QLabel(platform.system()))
        system_layout.addRow("Platform:", QLabel(platform.platform()))
        
        if PSUTIL_AVAILABLE:
            system_layout.addRow("CPU Cores:", QLabel(str(psutil.cpu_count())))
            memory_gb = psutil.virtual_memory().total / (1024**3)
            system_layout.addRow("Total Memory:", QLabel(f"{memory_gb:.1f} GB"))
        else:
            system_layout.addRow("CPU Cores:", QLabel("Unknown"))
            system_layout.addRow("Total Memory:", QLabel("Unknown"))
        
        layout.addWidget(system_group)
        
        # AreTomo3 Configuration
        aretomo_group = QGroupBox("🔬 AreTomo3 Configuration")
        aretomo_layout = QVBoxLayout(aretomo_group)
        
        self.config_text = QTextEdit()
        self.config_text.setMaximumHeight(150)
        self.config_text.setReadOnly(True)
        self.config_text.setPlainText("Configuration will be displayed here when processing starts...")
        
        aretomo_layout.addWidget(self.config_text)
        layout.addWidget(aretomo_group)
        
        # Processing Status
        status_group = QGroupBox("📊 Processing Status")
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(120)
        self.status_text.setReadOnly(True)
        self.status_text.setPlainText("No processing active...")
        
        status_layout.addWidget(self.status_text)
        layout.addWidget(status_group)
        
        layout.addStretch()
        return panel
        
    def start_monitoring(self):
        """Start the performance monitoring timer."""
        if not PSUTIL_AVAILABLE:
            return
            
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_performance_metrics)
        self.monitor_timer.start(2000)  # Update every 2 seconds
        
    def update_performance_metrics(self):
        """Update performance metrics."""
        if not PSUTIL_AVAILABLE:
            return
            
        try:
            # CPU Usage
            cpu_percent = psutil.cpu_percent()
            self.cpu_label.setText(f"CPU: {cpu_percent:.1f}%")
            self.cpu_progress.setValue(int(cpu_percent))
            
            # Memory Usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_gb = memory.used / (1024**3)
            self.memory_label.setText(f"Memory: {memory_percent:.1f}% ({memory_used_gb:.1f} GB)")
            self.memory_progress.setValue(int(memory_percent))
            
            # Disk Usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_used_gb = disk.used / (1024**3)
            self.disk_label.setText(f"Disk: {disk_percent:.1f}% ({disk_used_gb:.1f} GB)")
            self.disk_progress.setValue(int(disk_percent))
            
            # GPU Usage
            self.update_gpu_info()
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
            
    def update_gpu_info(self):
        """Update GPU information."""
        try:
            if GPUTIL_AVAILABLE:
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu = gpus[0]
                    gpu_load = gpu.load * 100
                    gpu_memory = (gpu.memoryUsed / gpu.memoryTotal) * 100
                    self.gpu_label.setText(
                        f"GPU: {gpu_load:.1f}% load, {gpu_memory:.1f}% memory\n{gpu.name}"
                    )
                else:
                    self.gpu_label.setText("GPU: No NVIDIA GPU detected")
            else:
                self.gpu_label.setText("GPU: GPUtil not available\n(pip install gputil)")
        except Exception as e:
            self.gpu_label.setText(f"GPU: Error reading status\n{str(e)[:50]}...")
            
    def update_configuration(self, config_text: str):
        """Update the configuration display."""
        self.config_text.setPlainText(config_text)
        
    def update_status(self, status_text: str):
        """Update the processing status display."""
        self.status_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {status_text}")
        

    def update_processing_status(self, status: str, message: str, progress: int = 0):
        """Update processing status with detailed information."""
        status_message = f"Status: {status.title()} - {message}"
        self.update_status(status_message)
        if status == "processing":
            config_text = f"🔬 AreTomo3 Configuration\nStatus: {status}\nMessage: {message}\nTime: {datetime.now().strftime("%H:%M:%S")}"
            self.update_configuration(config_text)

    def closeEvent(self, event):
        """Handle close event."""
        if self.monitor_timer:
            self.monitor_timer.stop()
        event.accept()
