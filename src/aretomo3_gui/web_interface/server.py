"""
Web-Based Interface
Browser-based access with real-time monitoring
"""

from flask import Flask, jsonify, request
from flask_socketio import Socket<PERSON>, emit
import json
import threading
import time
from typing import Dict, List, Any
import numpy as np
import base64
import io
import matplotlib.pyplot as plt
from datetime import datetime


class WebInterface:
    """Main web interface coordinator."""
    
    def __init__(self, host='0.0.0.0', port=8080):
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'aretomo3_gui_secret'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        self.host = host
        self.port = port
        
        # Application state
        self.processing_status = {
            'active_jobs': [],
            'completed_jobs': [],
            'failed_jobs': [],
            'system_status': 'idle'
        }
        
        self.setup_routes()
        
    def setup_routes(self):
        """Setup Flask routes."""
        
        @self.app.route('/')
        def index():
            """Main dashboard."""
            return {
                'message': 'AreTomo3 GUI Web Interface',
                'version': '2.0.0',
                'status': 'running'
            }
            
        @self.app.route('/api/status')
        def get_status():
            """Get current system status."""
            return jsonify(self.processing_status)
            
        @self.app.route('/api/jobs', methods=['GET'])
        def get_jobs():
            """Get all jobs."""
            return jsonify({
                'active': self.processing_status['active_jobs'],
                'completed': self.processing_status['completed_jobs'],
                'failed': self.processing_status['failed_jobs']
            })
            
        @self.app.route('/api/jobs', methods=['POST'])
        def create_job():
            """Create new processing job."""
            job_data = request.json
            job_id = f"job_{len(self.processing_status['active_jobs']) + 1:06d}"
            
            new_job = {
                'id': job_id,
                'name': job_data.get('name', 'Untitled Job'),
                'type': job_data.get('type', 'reconstruction'),
                'parameters': job_data.get('parameters', {}),
                'status': 'queued',
                'progress': 0.0,
                'created_time': datetime.now().isoformat()
            }
            
            self.processing_status['active_jobs'].append(new_job)
            return jsonify({'success': True, 'job_id': job_id})
            
    def run(self, debug=False):
        """Run the web interface."""
        print(f"🌐 Starting web interface on http://{self.host}:{self.port}")
        self.socketio.run(self.app, host=self.host, port=self.port, debug=debug)


# Global web interface
web_interface = WebInterface()
