#!/usr/bin/env python3
"""
Comprehensive CTF Analysis Dashboard

This module provides a complete dashboard combining:
- Interactive 2D CTF visualization
- Quality assessment plots
- Analysis summary
- Export capabilities
"""

import logging
from typing import Dict, List, Optional, Tuple

import matplotlib.gridspec as gridspec
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.widgets import <PERSON>ton, Slider

from .ctf_parser import CTFDataParser
from .ctf_quality import CTFQualityAssessment
from .ctf_visualizer import CTF2DVisualizer

logger = logging.getLogger(__name__)


class CTFAnalysisDashboard:
    """
    Comprehensive CTF analysis dashboard.

    Combines:
    - Interactive 2D CTF viewer
    - Quality assessment plots
    - Summary statistics
    - Export functionality
    """

    def __init__(self, ctf_data: Dict):
        """
        Initialize the CTF analysis dashboard.

        Args:
            ctf_data: Dictionary containing parsed CTF data
        """
        self.ctf_data = ctf_data
        self.series_name = ctf_data.get("series_name", "Unknown")

        # Initialize components
        self.visualizer = None
        self.quality_assessor = None
        self.quality_summary = None

        # Initialize quality assessment
        if ctf_data.get("parameters") is not None:
            self.quality_assessor = CTFQualityAssessment(ctf_data)
            self.quality_summary = self.quality_assessor.assess_all_quality()

        # Dashboard state
        self.current_tilt_idx = 0
        self.fig = None
        self.axes = {}

        logger.info(
            f"Initialized CTF analysis dashboard for {self.series_name}"
        )

    def create_dashboard(self):
        """Create the comprehensive analysis dashboard."""
        # Create figure with complex layout
        self.fig = plt.figure(figsize=(16, 12))
        self.fig.suptitle(
            f"CTF Analysis Dashboard - {self.series_name}",
            fontsize=16,
            fontweight="bold",
        )

        # Create grid layout
        gs = gridspec.GridSpec(4, 6, figure=self.fig, hspace=0.3, wspace=0.3)

        # Main 2D CTF viewer (top-left, large)
        self.axes["main_2d"] = self.fig.add_subplot(gs[0:2, 0:3])

        # Quality plots (top-right)
        self.axes["defocus_plot"] = self.fig.add_subplot(gs[0, 3:5])
        self.axes["resolution_plot"] = self.fig.add_subplot(gs[0, 5])
        self.axes["quality_plot"] = self.fig.add_subplot(gs[1, 3:5])
        self.axes["astigmatism_plot"] = self.fig.add_subplot(gs[1, 5])

        # Summary and info (middle)
        self.axes["summary"] = self.fig.add_subplot(gs[2, 0:2])
        self.axes["info"] = self.fig.add_subplot(gs[2, 2:4])
        self.axes["recommendations"] = self.fig.add_subplot(gs[2, 4:6])

        # Controls and slider (bottom)
        self.axes["controls"] = self.fig.add_subplot(gs[3, 0:2])
        self.axes["slider"] = self.fig.add_subplot(gs[3, 2:6])

        # Initialize all components
        self._setup_2d_viewer()
        self._setup_quality_plots()
        self._setup_summary_panels()
        self._setup_controls()

        return self.fig

    def _setup_2d_viewer(self):
        """Set up the main 2D CTF viewer."""
        if self.ctf_data.get("power_spectra") is None:
            self.axes["main_2d"].text(
                0.5,
                0.5,
                "No Power Spectra Available",
                ha="center",
                va="center",
                transform=self.axes["main_2d"].transAxes,
            )
            self.axes["main_2d"].set_title("2D CTF Power Spectrum")
            return

        # Display initial power spectrum
        self.power_spectra = self.ctf_data["power_spectra"]
        self.n_tilts = self.power_spectra.shape[0]

        # Show first tilt
        initial_spectrum = self.power_spectra[0]
        display_data = np.log10(np.maximum(initial_spectrum, 1e-10))

        self.im_2d = self.axes["main_2d"].imshow(
            display_data, cmap="gray", origin="lower", interpolation="bilinear"
        )

        self.axes["main_2d"].set_title("2D CTF Power Spectrum - Tilt 1")
        self.axes["main_2d"].set_xlabel("Frequency")
        self.axes["main_2d"].set_ylabel("Frequency")

    # TODO: Refactor function - Function '_setup_quality_plots' too long (81
    # lines)
    def _setup_quality_plots(self):
        """Set up quality assessment plots."""
        if not self.quality_summary:
            for ax_name in [
                "defocus_plot",
                "resolution_plot",
                "quality_plot",
                "astigmatism_plot",
            ]:
                self.axes[ax_name].text(
                    0.5,
                    0.5,
                    "No Quality Data",
                    ha="center",
                    va="center",
                    transform=self.axes[ax_name].transAxes,
                )
            return

        parameters = self.ctf_data["parameters"]
        tilt_angles = self.ctf_data.get("tilt_angles", range(len(parameters)))

        # Defocus vs tilt angle
        self.axes["defocus_plot"].plot(
            tilt_angles, parameters["defocus1_A"] / 10000, "b-", label="Defocus 1"
        )
        self.axes["defocus_plot"].plot(
            tilt_angles, parameters["defocus2_A"] / 10000, "r-", label="Defocus 2"
        )
        self.axes["defocus_plot"].set_xlabel("Tilt Angle (°)")
        self.axes["defocus_plot"].set_ylabel("Defocus (μm)")
        self.axes["defocus_plot"].set_title("Defocus vs Tilt")
        self.axes["defocus_plot"].legend(fontsize=8)
        self.axes["defocus_plot"].grid(True, alpha=0.3)

        # Resolution vs tilt angle
        self.axes["resolution_plot"].plot(
            tilt_angles, parameters["resolution_limit_A"], "g-"
        )
        self.axes["resolution_plot"].set_xlabel("Tilt Angle (°)")
        self.axes["resolution_plot"].set_ylabel("Resolution (Å)")
        self.axes["resolution_plot"].set_title("Resolution")
        self.axes["resolution_plot"].grid(True, alpha=0.3)

        # Quality scores
        if "overall_score" in self.quality_summary["quality_scores"]:
            scores = self.quality_summary["quality_scores"]["overall_score"]
            colors = [
                "red" if i in self.quality_summary["outliers"] else "blue"
                for i in range(len(scores))
            ]

            self.axes["quality_plot"].scatter(tilt_angles, scores, c=colors, alpha=0.7)
            self.axes["quality_plot"].set_xlabel("Tilt Angle (°)")
            self.axes["quality_plot"].set_ylabel("Quality Score")
            self.axes["quality_plot"].set_title("Quality Scores")
            self.axes["quality_plot"].grid(True, alpha=0.3)

            # Add quality threshold lines
            self.axes["quality_plot"].axhline(
                y=80, color="green", linestyle="--", alpha=0.5, label="Excellent"
            )
            self.axes["quality_plot"].axhline(
                y=65, color="orange", linestyle="--", alpha=0.5, label="Good"
            )
            self.axes["quality_plot"].axhline(
                y=50, color="red", linestyle="--", alpha=0.5, label="Fair"
            )

        # Astigmatism
        astigmatism = (
            np.abs(parameters["defocus1_A"] - parameters["defocus2_A"]) / 10000
        )
        self.axes["astigmatism_plot"].plot(tilt_angles, astigmatism, "purple")
        self.axes["astigmatism_plot"].set_xlabel("Tilt Angle (°)")
        self.axes["astigmatism_plot"].set_ylabel("Astigmatism (μm)")
        self.axes["astigmatism_plot"].set_title("Astigmatism")
        self.axes["astigmatism_plot"].grid(True, alpha=0.3)

        # Add current tilt indicator lines
        self._add_tilt_indicators()

    def _add_tilt_indicators(self):
        """Add vertical lines indicating current tilt position."""
        if not self.ctf_data.get("tilt_angles"):
            return

        current_angle = self.ctf_data["tilt_angles"][self.current_tilt_idx]

        # Add to all plots
        for ax_name in [
            "defocus_plot",
            "resolution_plot",
            "quality_plot",
            "astigmatism_plot",
        ]:
            if hasattr(self, f"_tilt_line_{ax_name}"):
                getattr(self, f"_tilt_line_{ax_name}").remove()

            line = self.axes[ax_name].axvline(
                x=current_angle, color="red", linestyle="-", alpha=0.8, linewidth=2
            )
            setattr(self, f"_tilt_line_{ax_name}", line)

    # TODO: Refactor function - Function '_setup_summary_panels' too long (56
    # lines)
    def _setup_summary_panels(self):
        """Set up summary information panels."""
        # Clear axes
        for ax_name in ["summary", "info", "recommendations"]:
            self.axes[ax_name].clear()
            self.axes[ax_name].axis("off")

        if not self.quality_summary:
            return

        # Overall summary
        overall = self.quality_summary["overall_quality"]
        summary_text = f"""Overall Quality Assessment: Grade: {overall['grade']} Score: {overall['score']:.1f}/100 Statistics: • Mean Score: {overall['mean_individual_score']:.1f} • Std Dev: {overall['score_std']:.1f} • Outliers: {overall['outlier_count']}/{self.quality_summary['n_tilts']} """

        self.axes["summary"].text(
            0.05,
            0.95,
            summary_text,
            transform=self.axes["summary"].transAxes,
            fontsize=10,
            verticalalignment="top",
            fontfamily="monospace",
        )
        self.axes["summary"].set_title("Quality Summary", fontweight="bold")

        # Current tilt info
        self._update_current_tilt_info()

        # Recommendations
        recommendations = self.quality_summary.get("recommendations", [])
        if recommendations:
            rec_text = "Recommendations:\n\n"
            for i, rec in enumerate(recommendations[:5], 1):  # Show first 5
                rec_text += f"{i}. {rec}\n\n"
        else:
            rec_text = "No specific recommendations.\nCTF quality appears acceptable."

        self.axes["recommendations"].text(
            0.05,
            0.95,
            rec_text,
            transform=self.axes["recommendations"].transAxes,
            fontsize=9,
            verticalalignment="top",
            wrap=True,
        )
        self.axes["recommendations"].set_title("Recommendations", fontweight="bold")

    # TODO: Refactor function - Function '_update_current_tilt_info' too long
    # (51 lines)
    def _update_current_tilt_info(self):
        """Update current tilt information panel."""
        self.axes["info"].clear()
        self.axes["info"].axis("off")

        if not self.quality_summary or self.current_tilt_idx >= len(
            self.ctf_data["parameters"]
        ):
            return

        # Get current tilt data
        current_params = self.ctf_data["parameters"].iloc[self.current_tilt_idx]
        tilt_angle = (
            self.ctf_data["tilt_angles"][self.current_tilt_idx]
            if self.ctf_data.get("tilt_angles")
            else "N/A"
        )

        # Get quality info
        quality_info = self.quality_assessor.get_tilt_quality_info(
            self.current_tilt_idx
        )

        info_text = f"""Current Tilt Information: Tilt: {self.current_tilt_idx + 1}/{self.quality_summary['n_tilts']} Angle: {tilt_angle}° CTF Parameters: • Defocus 1: {current_params['defocus1_A'] / 10000:.2f} μm • Defocus 2: {current_params['defocus2_A'] / 10000:.2f} μm • Astigmatism: {current_params['astigmatism_angle']:.1f}° • Resolution: {current_params['resolution_limit_A']:.1f} Å Quality Scores: • Overall: {quality_info['scores']['overall_score']:.1f} • Cross Corr: {quality_info['scores']['cross_correlation_score']:.1f} • Resolution: {quality_info['scores']['resolution_score']:.1f} • Outlier: {'Yes' if quality_info['is_outlier'] else 'No'} """

        self.axes["info"].text(
            0.05,
            0.95,
            info_text,
            transform=self.axes["info"].transAxes,
            fontsize=9,
            verticalalignment="top",
            fontfamily="monospace",
        )
        self.axes["info"].set_title("Current Tilt Info", fontweight="bold")

    def _setup_controls(self):
        """Set up control buttons and slider."""
        # Clear controls
        self.axes["controls"].clear()
        self.axes["controls"].axis("off")
        self.axes["slider"].clear()

        if self.ctf_data.get("power_spectra") is None:
            return

        # Create slider
        self.slider = Slider(
            self.axes["slider"],
            "Tilt Index",
            0,
            self.n_tilts - 1,
            valinit=0,
            valfmt="%d",
            valstep=1,
        )
        self.slider.on_changed(self.update_tilt)

        # Add control buttons (simplified for dashboard)
        button_text = """Controls:

• Use slider to navigate tilts
• Red line shows current tilt
• Red points show outliers
• Export functionality available
"""

        self.axes["controls"].text(
            0.05,
            0.95,
            button_text,
            transform=self.axes["controls"].transAxes,
            fontsize=10,
            verticalalignment="top",
        )
        self.axes["controls"].set_title("Controls", fontweight="bold")

    def update_tilt(self, val):
        """Update display when slider value changes."""
        self.current_tilt_idx = int(self.slider.val)

        # Update 2D viewer
        if self.power_spectra is not None:
            current_spectrum = self.power_spectra[self.current_tilt_idx]
            display_data = np.log10(np.maximum(current_spectrum, 1e-10))

            self.im_2d.set_data(display_data)
            self.im_2d.set_clim(vmin=display_data.min(), vmax=display_data.max())

            # Update title
            tilt_angle = (
                self.ctf_data["tilt_angles"][self.current_tilt_idx]
                if self.ctf_data.get("tilt_angles")
                else self.current_tilt_idx
            )
            self.axes["main_2d"].set_title(
                f"2D CTF Power Spectrum - Tilt {self.current_tilt_idx + 1} ({tilt_angle:.1f}°)"
            )

        # Update tilt indicators
        self._add_tilt_indicators()

        # Update current tilt info
        self._update_current_tilt_info()

        self.fig.canvas.draw()

    def export_summary(self, filename: str = None):
        """Export quality summary to file."""
        if not filename:
            filename = f"{self.series_name}_ctf_analysis_summary.txt"

        if not self.quality_summary:
            logger.warning("No quality summary available for export")
            return

        with open(filename, "w") as f:
            f.write(f"CTF Analysis Summary - {self.series_name}\n")
            f.write("=" * 50 + "\n\n")

            # Overall quality
            overall = self.quality_summary["overall_quality"]
            f.write(
                f"Overall Quality: {overall['grade']} ({overall['score']:.1f}/100)\n"
            )
            f.write(
                f"Mean Individual Score: {overall['mean_individual_score']:.1f}\n"
            )
            f.write(f"Score Standard Deviation: {overall['score_std']:.1f}\n")
            f.write(f"Number of Outliers: {overall['outlier_count']}\n\n")

            # Resolution analysis
            if self.quality_summary.get("resolution_analysis"):
                res = self.quality_summary["resolution_analysis"]
                f.write("Resolution Analysis:\n")
                f.write(f"Mean Resolution: {res['mean_resolution']:.1f} Å\n")
                f.write(f"Best Resolution: {res['best_resolution']:.1f} Å\n")
                f.write(f"Worst Resolution: {res['worst_resolution']:.1f} Å\n")
                f.write(f"Trend Quality: {res['trend_quality']}\n\n")

            # Recommendations
            recommendations = self.quality_summary.get("recommendations", [])
            if recommendations:
                f.write("Recommendations:\n")
                for i, rec in enumerate(recommendations, 1):
                    f.write(f"{i}. {rec}\n")

        logger.info(f"Exported CTF analysis summary to {filename}")

    def show(self):
        """Display the dashboard."""
        if self.fig is None:
            self.create_dashboard()
        plt.show()
        return self.fig


def test_ctf_dashboard():
    """Test function for CTF dashboard."""
    import sys

    if len(sys.argv) > 1:
        test_path = sys.argv[1]
    else:
        test_path = "sample_data/test_batch/aretomo_output"

    try:
        # Parse CTF data
        parser = CTFDataParser(test_path)
        ctf_data = parser.parse_all()

        # Create dashboard
        dashboard = CTFAnalysisDashboard(ctf_data)

        logger.info(
            f"Created CTF analysis dashboard for {ctf_data['series_name']}"
        )
        logger.info("Use slider to navigate through tilts")
        logger.info("Red lines indicate current tilt position")
        logger.info("Red points in quality plot indicate outliers")

        # Show dashboard
        dashboard.show()

        # Export summary
        dashboard.export_summary()
        logger.info("Exported analysis summary")

        return True

    except Exception as e:
        logger.info(f"Error testing CTF dashboard: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_ctf_dashboard()
