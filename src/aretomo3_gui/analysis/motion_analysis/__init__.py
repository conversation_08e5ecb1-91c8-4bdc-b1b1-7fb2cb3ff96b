"""
Motion Analysis Module for AreTomo3 GUI

This module provides comprehensive motion correction analysis and visualization
capabilities for AreTomo3 output data.

Components:
- MotionCorrectionParser: Parse motion correction data from AreTomo3 output
- MotionCorrectionVisualizer: Interactive visualization of motion correction results
"""

from .motion_parser import MotionCorrectionParser
from .motion_visualizer import MotionCorrectionVisualizer

__all__ = ["MotionCorrectionParser", "MotionCorrectionVisualizer"]

__version__ = "1.0.0"
__author__ = "AreTomo3 GUI Development Team"
