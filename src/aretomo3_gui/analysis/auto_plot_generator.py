#!/usr/bin/env python3
"""
Automatic Plot Generator for AreTomo3 Results
Generates all plots automatically when processing completes
"""

import os
import logging
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend for threading safety
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd

# Import our plot theme manager
try:
    from ..gui.plot_theme_manager import plot_theme_manager, create_themed_figure, style_themed_axes, save_themed_figure
    THEME_AVAILABLE = True
except ImportError:
    THEME_AVAILABLE = False

logger = logging.getLogger(__name__)

class AutoPlotGenerator:
    """Automatic plot generator for AreTomo3 results."""
    
    def __init__(self, theme="light"):
        """Initialize the plot generator."""
        self.theme = theme
        self.generated_plots = {}
        self.plot_config = {
            'dpi': 300,
            'format': 'png',
            'figsize': (12, 8),
            'style': 'default'
        }
        
        # Apply theme if available
        if THEME_AVAILABLE:
            plot_theme_manager.set_theme(theme)
            logger.info(f"Applied {theme} theme to plot generator")
        else:
            # Fallback to white background for light theme
            if theme == "light":
                plt.style.use('default')
                plt.rcParams.update({
                    'figure.facecolor': 'white',
                    'axes.facecolor': 'white',
                    'savefig.facecolor': 'white'
                })
            logger.warning("Plot theme manager not available, using fallback styling")
    
    def generate_all_plots(self, analysis_data: Dict[str, Any], output_dir: str) -> Dict[str, str]:
        """Generate all plots based on analysis data."""
        plots_dir = Path(output_dir) / "plots"
        plots_dir.mkdir(exist_ok=True)
        
        logger.info(f"Generating plots in: {plots_dir}")
        
        data_summary = analysis_data.get('data_summary', {})
        plots_needed = analysis_data.get('plots_needed', [])
        
        generated_plots = {}
        
        # Generate each required plot
        for plot_type in plots_needed:
            try:
                plot_path = self._generate_plot(plot_type, data_summary, plots_dir)
                if plot_path:
                    generated_plots[plot_type] = plot_path
                    logger.info(f"Generated {plot_type}: {plot_path}")
            except Exception as e:
                logger.error(f"Error generating {plot_type}: {e}")
        
        # Generate summary dashboard
        if generated_plots:
            dashboard_path = self._generate_summary_dashboard(data_summary, plots_dir)
            if dashboard_path:
                generated_plots['summary_dashboard'] = dashboard_path
        
        self.generated_plots = generated_plots
        logger.info(f"Generated {len(generated_plots)} plots successfully")
        
        return generated_plots
    
    def _generate_plot(self, plot_type: str, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate a specific plot type."""
        plot_methods = {
            # Motion correction plots (WORKING)
            'motion_correction_plot': self._plot_motion_correction,
            'frame_alignment_plot': self._plot_frame_alignment,
            'motion_statistics_plot': self._plot_motion_statistics,

            # CTF analysis plots (WORKING)
            'ctf_estimation_plot': self._plot_ctf_estimation,
            'defocus_plot': self._plot_defocus,
            'astigmatism_plot': self._plot_astigmatism,

            # Summary plots (WORKING)
            'tilt_series_summary_plot': self._plot_tilt_series_summary,
            'quality_metrics_plot': self._plot_quality_metrics,
            'processing_overview_plot': self._plot_processing_overview

            # NOTE: Removed non-existent methods:
            # - 'tilt_alignment_plot': self._plot_tilt_alignment (NOT IMPLEMENTED)
            # - 'alignment_quality_plot': self._plot_alignment_quality (NOT IMPLEMENTED)
        }
        
        plot_method = plot_methods.get(plot_type)
        if plot_method:
            return plot_method(data_summary, plots_dir)
        else:
            logger.warning(f"Unknown plot type: {plot_type}")
            return None
    
    def _plot_motion_correction(self, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate motion correction plot."""
        motion_data = data_summary.get('motion_data', [])
        if not motion_data:
            return None
        
        # Create themed figure
        if THEME_AVAILABLE:
            fig = create_themed_figure(figsize=self.plot_config['figsize'])
        else:
            fig = plt.figure(figsize=self.plot_config['figsize'], facecolor='white')
        
        # Create subplots for X and Y motion
        ax1 = fig.add_subplot(2, 1, 1)
        ax2 = fig.add_subplot(2, 1, 2)
        
        for i, motion in enumerate(motion_data):
            frames = motion.get('frames', [])
            x_shifts = motion.get('x_shifts', [])
            y_shifts = motion.get('y_shifts', [])

            if len(frames) > 0 and len(x_shifts) > 0 and len(y_shifts) > 0:
                # Plot X motion
                ax1.plot(frames, x_shifts, label=f'Dataset {i+1}', linewidth=2)
                # Plot Y motion
                ax2.plot(frames, y_shifts, label=f'Dataset {i+1}', linewidth=2)
        
        # Style axes
        if THEME_AVAILABLE:
            style_themed_axes(ax1, title="X Motion Correction", xlabel="Frame", ylabel="X Shift (pixels)")
            style_themed_axes(ax2, title="Y Motion Correction", xlabel="Frame", ylabel="Y Shift (pixels)")
        else:
            ax1.set_title("X Motion Correction", fontweight='bold')
            ax1.set_xlabel("Frame")
            ax1.set_ylabel("X Shift (pixels)")
            ax1.grid(True, alpha=0.3)
            
            ax2.set_title("Y Motion Correction", fontweight='bold')
            ax2.set_xlabel("Frame")
            ax2.set_ylabel("Y Shift (pixels)")
            ax2.grid(True, alpha=0.3)
        
        ax1.legend()
        ax2.legend()
        
        plt.tight_layout()
        
        # Save plot
        plot_path = plots_dir / "motion_correction.png"
        if THEME_AVAILABLE:
            save_themed_figure(fig, plot_path, dpi=self.plot_config['dpi'])
        else:
            fig.savefig(plot_path, dpi=self.plot_config['dpi'], facecolor='white', bbox_inches='tight')
        
        plt.close(fig)
        return str(plot_path)
    
    def _plot_frame_alignment(self, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate frame alignment plot."""
        motion_data = data_summary.get('motion_data', [])
        if not motion_data:
            return None
        
        # Create themed figure
        if THEME_AVAILABLE:
            fig = create_themed_figure(figsize=self.plot_config['figsize'])
        else:
            fig = plt.figure(figsize=self.plot_config['figsize'], facecolor='white')
        
        ax = fig.add_subplot(1, 1, 1)
        
        for i, motion in enumerate(motion_data):
            x_shifts = motion.get('x_shifts', [])
            y_shifts = motion.get('y_shifts', [])
            
            if len(x_shifts) > 0 and len(y_shifts) > 0:
                # Create trajectory plot
                ax.plot(x_shifts, y_shifts, 'o-', label=f'Dataset {i+1}', alpha=0.7, markersize=4)
                
                # Mark start and end points
                if len(x_shifts) > 0:
                    ax.plot(x_shifts[0], y_shifts[0], 'go', markersize=8, label='Start' if i == 0 else "")
                    ax.plot(x_shifts[-1], y_shifts[-1], 'ro', markersize=8, label='End' if i == 0 else "")
        
        # Style axes
        if THEME_AVAILABLE:
            style_themed_axes(ax, title="Frame Alignment Trajectory", xlabel="X Shift (pixels)", ylabel="Y Shift (pixels)")
        else:
            ax.set_title("Frame Alignment Trajectory", fontweight='bold')
            ax.set_xlabel("X Shift (pixels)")
            ax.set_ylabel("Y Shift (pixels)")
            ax.grid(True, alpha=0.3)
        
        ax.legend()
        ax.axis('equal')
        
        plt.tight_layout()
        
        # Save plot
        plot_path = plots_dir / "frame_alignment.png"
        if THEME_AVAILABLE:
            save_themed_figure(fig, plot_path, dpi=self.plot_config['dpi'])
        else:
            fig.savefig(plot_path, dpi=self.plot_config['dpi'], facecolor='white', bbox_inches='tight')
        
        plt.close(fig)
        return str(plot_path)
    
    def _plot_motion_statistics(self, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate motion statistics plot."""
        motion_data = data_summary.get('motion_data', [])
        if not motion_data:
            return None
        
        # Create themed figure
        if THEME_AVAILABLE:
            fig = create_themed_figure(figsize=self.plot_config['figsize'])
        else:
            fig = plt.figure(figsize=self.plot_config['figsize'], facecolor='white')
        
        # Create subplots for statistics
        ax1 = fig.add_subplot(2, 2, 1)
        ax2 = fig.add_subplot(2, 2, 2)
        ax3 = fig.add_subplot(2, 2, 3)
        ax4 = fig.add_subplot(2, 2, 4)
        
        all_x_shifts = []
        all_y_shifts = []
        
        for motion in motion_data:
            x_shifts = motion.get('x_shifts', [])
            y_shifts = motion.get('y_shifts', [])
            all_x_shifts.extend(x_shifts)
            all_y_shifts.extend(y_shifts)
        
        if len(all_x_shifts) > 0 and len(all_y_shifts) > 0:
            # X shift histogram
            ax1.hist(all_x_shifts, bins=30, alpha=0.7, edgecolor='black')
            ax1.set_title("X Shift Distribution")
            ax1.set_xlabel("X Shift (pixels)")
            ax1.set_ylabel("Frequency")
            
            # Y shift histogram
            ax2.hist(all_y_shifts, bins=30, alpha=0.7, edgecolor='black')
            ax2.set_title("Y Shift Distribution")
            ax2.set_xlabel("Y Shift (pixels)")
            ax2.set_ylabel("Frequency")
            
            # Motion magnitude
            motion_magnitude = np.sqrt(np.array(all_x_shifts)**2 + np.array(all_y_shifts)**2)
            ax3.hist(motion_magnitude, bins=30, alpha=0.7, edgecolor='black')
            ax3.set_title("Motion Magnitude Distribution")
            ax3.set_xlabel("Motion Magnitude (pixels)")
            ax3.set_ylabel("Frequency")
            
            # Statistics text
            stats_text = f"""Motion Statistics:
X Shift: {np.mean(all_x_shifts):.2f} ± {np.std(all_x_shifts):.2f}
Y Shift: {np.mean(all_y_shifts):.2f} ± {np.std(all_y_shifts):.2f}
Magnitude: {np.mean(motion_magnitude):.2f} ± {np.std(motion_magnitude):.2f}
Max Motion: {np.max(motion_magnitude):.2f} pixels
Total Frames: {len(all_x_shifts)}"""
            
            ax4.text(0.1, 0.5, stats_text, transform=ax4.transAxes, fontsize=10, 
                    verticalalignment='center', fontfamily='monospace')
            ax4.set_title("Motion Statistics")
            ax4.axis('off')
        
        plt.tight_layout()
        
        # Save plot
        plot_path = plots_dir / "motion_statistics.png"
        if THEME_AVAILABLE:
            save_themed_figure(fig, plot_path, dpi=self.plot_config['dpi'])
        else:
            fig.savefig(plot_path, dpi=self.plot_config['dpi'], facecolor='white', bbox_inches='tight')
        
        plt.close(fig)
        return str(plot_path)
    
    def _plot_ctf_estimation(self, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate CTF estimation plot."""
        ctf_data = data_summary.get('ctf_data', {})
        if not ctf_data or not ctf_data.get('per_tilt_data'):
            return None
        
        # Create themed figure
        if THEME_AVAILABLE:
            fig = create_themed_figure(figsize=self.plot_config['figsize'])
        else:
            fig = plt.figure(figsize=self.plot_config['figsize'], facecolor='white')
        
        ax = fig.add_subplot(1, 1, 1)
        
        per_tilt_data = ctf_data['per_tilt_data']
        tilt_indices = [entry['tilt_index'] for entry in per_tilt_data]
        defocus1 = [entry['defocus1'] for entry in per_tilt_data]
        defocus2 = [entry['defocus2'] for entry in per_tilt_data]
        
        # Plot defocus values
        ax.plot(tilt_indices, defocus1, 'o-', label='Defocus 1', linewidth=2, markersize=4)
        ax.plot(tilt_indices, defocus2, 's-', label='Defocus 2', linewidth=2, markersize=4)
        
        # Style axes
        if THEME_AVAILABLE:
            style_themed_axes(ax, title="CTF Estimation Results", xlabel="Tilt Index", ylabel="Defocus (Å)")
        else:
            ax.set_title("CTF Estimation Results", fontweight='bold')
            ax.set_xlabel("Tilt Index")
            ax.set_ylabel("Defocus (Å)")
            ax.grid(True, alpha=0.3)
        
        ax.legend()
        
        plt.tight_layout()
        
        # Save plot
        plot_path = plots_dir / "ctf_estimation.png"
        if THEME_AVAILABLE:
            save_themed_figure(fig, plot_path, dpi=self.plot_config['dpi'])
        else:
            fig.savefig(plot_path, dpi=self.plot_config['dpi'], facecolor='white', bbox_inches='tight')
        
        plt.close(fig)
        return str(plot_path)

    def _plot_defocus(self, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate defocus analysis plot."""
        return self._plot_defocus_plot(data_summary, plots_dir)

    def _plot_defocus_plot(self, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate defocus analysis plot."""
        ctf_data = data_summary.get('ctf_data', {})
        if not ctf_data or not ctf_data.get('per_tilt_data'):
            return None

        # Create themed figure
        if THEME_AVAILABLE:
            fig = create_themed_figure(figsize=self.plot_config['figsize'])
        else:
            fig = plt.figure(figsize=self.plot_config['figsize'], facecolor='white')

        # Create subplots
        ax1 = fig.add_subplot(2, 1, 1)
        ax2 = fig.add_subplot(2, 1, 2)

        per_tilt_data = ctf_data['per_tilt_data']
        tilt_indices = [entry['tilt_index'] for entry in per_tilt_data]
        defocus1 = [entry['defocus1'] for entry in per_tilt_data]
        defocus2 = [entry['defocus2'] for entry in per_tilt_data]

        # Average defocus
        avg_defocus = [(d1 + d2) / 2 for d1, d2 in zip(defocus1, defocus2)]

        # Plot average defocus vs tilt
        ax1.plot(tilt_indices, avg_defocus, 'o-', linewidth=2, markersize=4)
        if THEME_AVAILABLE:
            style_themed_axes(ax1, title="Average Defocus vs Tilt", xlabel="Tilt Index", ylabel="Average Defocus (Å)")
        else:
            ax1.set_title("Average Defocus vs Tilt", fontweight='bold')
            ax1.set_xlabel("Tilt Index")
            ax1.set_ylabel("Average Defocus (Å)")
            ax1.grid(True, alpha=0.3)

        # Defocus histogram
        ax2.hist(avg_defocus, bins=20, alpha=0.7, edgecolor='black')
        if THEME_AVAILABLE:
            style_themed_axes(ax2, title="Defocus Distribution", xlabel="Average Defocus (Å)", ylabel="Frequency")
        else:
            ax2.set_title("Defocus Distribution", fontweight='bold')
            ax2.set_xlabel("Average Defocus (Å)")
            ax2.set_ylabel("Frequency")
            ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        # Save plot
        plot_path = plots_dir / "defocus_analysis.png"
        if THEME_AVAILABLE:
            save_themed_figure(fig, plot_path, dpi=self.plot_config['dpi'])
        else:
            fig.savefig(plot_path, dpi=self.plot_config['dpi'], facecolor='white', bbox_inches='tight')

        plt.close(fig)
        return str(plot_path)

    def _plot_astigmatism(self, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate astigmatism plot."""
        ctf_data = data_summary.get('ctf_data', {})
        if not ctf_data or not ctf_data.get('per_tilt_data'):
            return None

        # Create themed figure
        if THEME_AVAILABLE:
            fig = create_themed_figure(figsize=self.plot_config['figsize'])
        else:
            fig = plt.figure(figsize=self.plot_config['figsize'], facecolor='white')

        ax = fig.add_subplot(1, 1, 1)

        per_tilt_data = ctf_data['per_tilt_data']
        tilt_indices = [entry['tilt_index'] for entry in per_tilt_data]
        astigmatism = [entry['astigmatism'] for entry in per_tilt_data]

        # Plot astigmatism
        ax.plot(tilt_indices, astigmatism, 'o-', linewidth=2, markersize=4)

        # Style axes
        if THEME_AVAILABLE:
            style_themed_axes(ax, title="Astigmatism vs Tilt", xlabel="Tilt Index", ylabel="Astigmatism (°)")
        else:
            ax.set_title("Astigmatism vs Tilt", fontweight='bold')
            ax.set_xlabel("Tilt Index")
            ax.set_ylabel("Astigmatism (°)")
            ax.grid(True, alpha=0.3)

        plt.tight_layout()

        # Save plot
        plot_path = plots_dir / "astigmatism.png"
        if THEME_AVAILABLE:
            save_themed_figure(fig, plot_path, dpi=self.plot_config['dpi'])
        else:
            fig.savefig(plot_path, dpi=self.plot_config['dpi'], facecolor='white', bbox_inches='tight')

        plt.close(fig)
        return str(plot_path)

    def _plot_tilt_series_summary(self, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate tilt series summary plot."""
        metrics = data_summary.get('tilt_series_metrics', {})
        if not metrics:
            return None

        # Create themed figure
        if THEME_AVAILABLE:
            fig = create_themed_figure(figsize=self.plot_config['figsize'])
        else:
            fig = plt.figure(figsize=self.plot_config['figsize'], facecolor='white')

        ax = fig.add_subplot(1, 1, 1)

        # Create summary text
        summary_text = f"""Tilt Series Summary:

Tilt Series: {metrics.get('Tilt_Series', 'N/A')}
Thickness: {metrics.get('Thickness(Pix)', 'N/A')} pixels
Tilt Axis: {metrics.get('Tilt_Axis', 'N/A')}°
Global Shift: {metrics.get('Global_Shift(Pix)', 'N/A')} pixels
Bad Patches (Low): {metrics.get('Bad_Patch_Low', 'N/A')}
Bad Patches (High): {metrics.get('Bad_Patch_High', 'N/A')}
Alignment Score: {metrics.get('Alignment_Score', 'N/A')}
Resolution: {metrics.get('Resolution(A)', 'N/A')} Å
"""

        ax.text(0.1, 0.5, summary_text, transform=ax.transAxes, fontsize=12,
                verticalalignment='center', fontfamily='monospace')

        if THEME_AVAILABLE:
            style_themed_axes(ax, title="Tilt Series Processing Summary")
        else:
            ax.set_title("Tilt Series Processing Summary", fontweight='bold')

        ax.axis('off')

        plt.tight_layout()

        # Save plot
        plot_path = plots_dir / "tilt_series_summary.png"
        if THEME_AVAILABLE:
            save_themed_figure(fig, plot_path, dpi=self.plot_config['dpi'])
        else:
            fig.savefig(plot_path, dpi=self.plot_config['dpi'], facecolor='white', bbox_inches='tight')

        plt.close(fig)
        return str(plot_path)

    def _plot_quality_metrics(self, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate quality metrics plot."""
        metrics = data_summary.get('tilt_series_metrics', {})
        if not metrics:
            return None

        # Create themed figure
        if THEME_AVAILABLE:
            fig = create_themed_figure(figsize=self.plot_config['figsize'])
        else:
            fig = plt.figure(figsize=self.plot_config['figsize'], facecolor='white')

        # Create subplots for different quality metrics
        ax1 = fig.add_subplot(2, 2, 1)
        ax2 = fig.add_subplot(2, 2, 2)
        ax3 = fig.add_subplot(2, 2, 3)
        ax4 = fig.add_subplot(2, 2, 4)

        # Quality metrics visualization
        quality_metrics = {
            'Alignment Score': metrics.get('Alignment_Score', 0),
            'Resolution (Å)': metrics.get('Resolution(A)', 0),
            'Global Shift': metrics.get('Global_Shift(Pix)', 0),
            'Bad Patches': metrics.get('Bad_Patch_Low', 0) + metrics.get('Bad_Patch_High', 0)
        }

        # Bar plot of quality metrics
        metric_names = list(quality_metrics.keys())
        metric_values = list(quality_metrics.values())

        ax1.bar(range(len(metric_names)), metric_values)
        ax1.set_xticks(range(len(metric_names)))
        ax1.set_xticklabels(metric_names, rotation=45, ha='right')
        ax1.set_title("Quality Metrics Overview")

        # Individual metric displays
        ax2.text(0.5, 0.5, f"Alignment Score\n{quality_metrics['Alignment Score']}",
                ha='center', va='center', transform=ax2.transAxes, fontsize=14, fontweight='bold')
        ax2.set_title("Alignment Quality")
        ax2.axis('off')

        ax3.text(0.5, 0.5, f"Resolution\n{quality_metrics['Resolution (Å)']} Å",
                ha='center', va='center', transform=ax3.transAxes, fontsize=14, fontweight='bold')
        ax3.set_title("Resolution")
        ax3.axis('off')

        ax4.text(0.5, 0.5, f"Global Shift\n{quality_metrics['Global Shift']} px",
                ha='center', va='center', transform=ax4.transAxes, fontsize=14, fontweight='bold')
        ax4.set_title("Global Shift")
        ax4.axis('off')

        plt.tight_layout()

        # Save plot
        plot_path = plots_dir / "quality_metrics.png"
        if THEME_AVAILABLE:
            save_themed_figure(fig, plot_path, dpi=self.plot_config['dpi'])
        else:
            fig.savefig(plot_path, dpi=self.plot_config['dpi'], facecolor='white', bbox_inches='tight')

        plt.close(fig)
        return str(plot_path)

    def _plot_processing_overview(self, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate processing overview plot."""
        # Create themed figure
        if THEME_AVAILABLE:
            fig = create_themed_figure(figsize=(16, 10))
        else:
            fig = plt.figure(figsize=(16, 10), facecolor='white')

        # Create a comprehensive overview with multiple subplots
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)

        # Motion correction overview
        if data_summary.get('motion_data'):
            ax1 = fig.add_subplot(gs[0, 0])
            motion_data = data_summary['motion_data'][0]
            x_shifts = motion_data.get('x_shifts', [])
            y_shifts = motion_data.get('y_shifts', [])
            if len(x_shifts) > 0 and len(y_shifts) > 0:
                motion_magnitude = np.sqrt(np.array(x_shifts)**2 + np.array(y_shifts)**2)
                ax1.plot(motion_magnitude, linewidth=2)
                ax1.set_title("Motion Magnitude")
                ax1.set_xlabel("Frame")
                ax1.set_ylabel("Motion (px)")

        # CTF overview
        if data_summary.get('ctf_data'):
            ax2 = fig.add_subplot(gs[0, 1])
            ctf_data = data_summary['ctf_data']
            per_tilt_data = ctf_data.get('per_tilt_data', [])
            if per_tilt_data:
                defocus1 = [entry['defocus1'] for entry in per_tilt_data]
                defocus2 = [entry['defocus2'] for entry in per_tilt_data]
                avg_defocus = [(d1 + d2) / 2 for d1, d2 in zip(defocus1, defocus2)]
                ax2.plot(avg_defocus, 'o-', linewidth=2, markersize=3)
                ax2.set_title("Average Defocus")
                ax2.set_xlabel("Tilt Index")
                ax2.set_ylabel("Defocus (Å)")

        # Processing summary
        ax3 = fig.add_subplot(gs[0, 2])
        metrics = data_summary.get('tilt_series_metrics', {})
        if metrics:
            summary_text = f"""Processing Summary:
Tilt Series: {metrics.get('Tilt_Series', 'N/A')}
Resolution: {metrics.get('Resolution(A)', 'N/A')} Å
Alignment Score: {metrics.get('Alignment_Score', 'N/A')}
Thickness: {metrics.get('Thickness(Pix)', 'N/A')} px"""
            ax3.text(0.1, 0.5, summary_text, transform=ax3.transAxes, fontsize=10,
                    verticalalignment='center', fontfamily='monospace')
            ax3.set_title("Processing Summary")
            ax3.axis('off')

        plt.suptitle("AreTomo3 Processing Overview", fontsize=16, fontweight='bold')

        # Save plot
        plot_path = plots_dir / "processing_overview.png"
        if THEME_AVAILABLE:
            save_themed_figure(fig, plot_path, dpi=self.plot_config['dpi'])
        else:
            fig.savefig(plot_path, dpi=self.plot_config['dpi'], facecolor='white', bbox_inches='tight')

        plt.close(fig)
        return str(plot_path)

    def _generate_summary_dashboard(self, data_summary: Dict[str, Any], plots_dir: Path) -> Optional[str]:
        """Generate a comprehensive summary dashboard."""
        # Create themed figure
        if THEME_AVAILABLE:
            fig = create_themed_figure(figsize=(20, 12))
        else:
            fig = plt.figure(figsize=(20, 12), facecolor='white')

        # Create comprehensive dashboard layout
        gs = fig.add_gridspec(4, 4, hspace=0.4, wspace=0.3)

        # Title
        fig.suptitle("AreTomo3 Processing Dashboard", fontsize=20, fontweight='bold')

        # Motion correction summary
        if data_summary.get('motion_data'):
            ax1 = fig.add_subplot(gs[0, 0])
            motion_data = data_summary['motion_data'][0]
            x_shifts = motion_data.get('x_shifts', [])
            y_shifts = motion_data.get('y_shifts', [])
            if len(x_shifts) > 0 and len(y_shifts) > 0:
                motion_magnitude = np.sqrt(np.array(x_shifts)**2 + np.array(y_shifts)**2)
                ax1.plot(motion_magnitude[:100], linewidth=2)  # Show first 100 frames
                ax1.set_title("Motion Magnitude")
                ax1.set_xlabel("Frame")
                ax1.set_ylabel("Motion (px)")

        # CTF summary
        if data_summary.get('ctf_data'):
            ax2 = fig.add_subplot(gs[0, 1])
            ctf_data = data_summary['ctf_data']
            per_tilt_data = ctf_data.get('per_tilt_data', [])
            if per_tilt_data:
                defocus1 = [entry['defocus1'] for entry in per_tilt_data]
                defocus2 = [entry['defocus2'] for entry in per_tilt_data]
                avg_defocus = [(d1 + d2) / 2 for d1, d2 in zip(defocus1, defocus2)]
                ax2.plot(avg_defocus, 'o-', linewidth=2, markersize=3)
                ax2.set_title("Average Defocus")
                ax2.set_xlabel("Tilt Index")
                ax2.set_ylabel("Defocus (Å)")

        # Processing metrics
        ax3 = fig.add_subplot(gs[0, 2:])
        metrics = data_summary.get('tilt_series_metrics', {})
        if metrics:
            summary_text = f"""Processing Summary:
Tilt Series: {metrics.get('Tilt_Series', 'N/A')}
Resolution: {metrics.get('Resolution(A)', 'N/A')} Å
Alignment Score: {metrics.get('Alignment_Score', 'N/A')}
Thickness: {metrics.get('Thickness(Pix)', 'N/A')} px
Tilt Axis: {metrics.get('Tilt_Axis', 'N/A')}°
Global Shift: {metrics.get('Global_Shift(Pix)', 'N/A')} px"""
            ax3.text(0.1, 0.5, summary_text, transform=ax3.transAxes, fontsize=12,
                    verticalalignment='center', fontfamily='monospace')
            ax3.set_title("Processing Summary")
            ax3.axis('off')

        # Save dashboard
        dashboard_path = plots_dir / "summary_dashboard.png"
        if THEME_AVAILABLE:
            save_themed_figure(fig, dashboard_path, dpi=self.plot_config['dpi'])
        else:
            fig.savefig(dashboard_path, dpi=self.plot_config['dpi'], facecolor='white', bbox_inches='tight')

        plt.close(fig)
        return str(dashboard_path)

# Convenience function
def generate_aretomo3_plots(analysis_data: Dict[str, Any], output_dir: str, theme: str = "light") -> Dict[str, str]:
    """Generate all AreTomo3 plots automatically."""
    generator = AutoPlotGenerator(theme=theme)
    return generator.generate_all_plots(analysis_data, output_dir)
