"""
Multi-Format Data Support
Universal data compatibility for tomography formats
"""

import numpy as np
import mrcfile
import h5py
import tifffile
from pathlib import Path
from typing import Union, Dict, Any, Optional, Tuple
import struct
from abc import ABC, abstractmethod


class DataFormat(ABC):
    """Abstract base class for data formats."""
    
    @abstractmethod
    def read(self, file_path: str) -> <PERSON>ple[np.ndarray, Dict[str, Any]]:
        """Read data from file."""
        pass
        
    @abstractmethod
    def write(self, file_path: str, data: np.ndarray, metadata: Dict[str, Any] = None):
        """Write data to file."""
        pass
        
    @abstractmethod
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """Get metadata from file."""
        pass


class MRCFormat(DataFormat):
    """MRC file format handler."""
    
    def read(self, file_path: str) -> <PERSON><PERSON>[np.ndarray, Dict[str, Any]]:
        """Read MRC file."""
        with mrcfile.open(file_path, mode='r') as mrc:
            data = mrc.data.copy()
            metadata = {
                'voxel_size': mrc.voxel_size,
                'origin': mrc.header.origin,
                'cell': {
                    'a': float(mrc.header.cella.x),
                    'b': float(mrc.header.cella.y), 
                    'c': float(mrc.header.cella.z)
                },
                'space_group': int(mrc.header.ispg),
                'machine_stamp': mrc.header.machst,
                'rms': float(mrc.header.rms),
                'min_value': float(mrc.header.dmin),
                'max_value': float(mrc.header.dmax),
                'mean_value': float(mrc.header.dmean)
            }
        return data, metadata
        
    def write(self, file_path: str, data: np.ndarray, metadata: Dict[str, Any] = None):
        """Write MRC file."""
        with mrcfile.new(file_path, overwrite=True) as mrc:
            mrc.set_data(data)
            if metadata:
                if 'voxel_size' in metadata:
                    mrc.voxel_size = metadata['voxel_size']
                    
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """Get MRC metadata."""
        _, metadata = self.read(file_path)
        return metadata


class HDF5Format(DataFormat):
    """HDF5 file format handler."""
    
    def read(self, file_path: str) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Read HDF5 file."""
        with h5py.File(file_path, 'r') as f:
            # Find main dataset (largest array)
            datasets = []
            f.visititems(lambda name, obj: datasets.append((name, obj)) 
                        if isinstance(obj, h5py.Dataset) else None)
            
            if not datasets:
                raise ValueError("No datasets found in HDF5 file")
                
            # Get largest dataset
            main_dataset = max(datasets, key=lambda x: x[1].size)
            data = main_dataset[1][:]
            
            # Extract metadata
            metadata = {}
            for key, value in f.attrs.items():
                metadata[key] = value
                
        return data, metadata
        
    def write(self, file_path: str, data: np.ndarray, metadata: Dict[str, Any] = None):
        """Write HDF5 file."""
        with h5py.File(file_path, 'w') as f:
            f.create_dataset('data', data=data, compression='gzip')
            if metadata:
                for key, value in metadata.items():
                    f.attrs[key] = value
                    
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """Get HDF5 metadata."""
        _, metadata = self.read(file_path)
        return metadata


class TIFFFormat(DataFormat):
    """TIFF file format handler."""
    
    def read(self, file_path: str) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Read TIFF file."""
        with tifffile.TiffFile(file_path) as tif:
            data = tif.asarray()
            
            # Extract metadata
            metadata = {}
            if tif.pages:
                page = tif.pages[0]
                metadata.update({
                    'shape': data.shape,
                    'dtype': str(data.dtype),
                    'compression': page.compression,
                    'photometric': page.photometric,
                    'planar_config': page.planarconfig
                })
                
                # Get resolution if available
                if hasattr(page, 'tags') and 'XResolution' in page.tags:
                    metadata['x_resolution'] = page.tags['XResolution'].value
                if hasattr(page, 'tags') and 'YResolution' in page.tags:
                    metadata['y_resolution'] = page.tags['YResolution'].value
                    
        return data, metadata
        
    def write(self, file_path: str, data: np.ndarray, metadata: Dict[str, Any] = None):
        """Write TIFF file."""
        tifffile.imwrite(file_path, data, compression='lzw')
        
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """Get TIFF metadata."""
        _, metadata = self.read(file_path)
        return metadata


class FormatManager:
    """Universal format manager."""
    
    def __init__(self):
        self.formats = {
            '.mrc': MRCFormat(),
            '.rec': MRCFormat(),
            '.st': MRCFormat(),
            '.ali': MRCFormat(),
            '.h5': HDF5Format(),
            '.hdf5': HDF5Format(),
            '.tif': TIFFFormat(),
            '.tiff': TIFFFormat()
        }
        
    def read_file(self, file_path: str) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Read file with automatic format detection."""
        path = Path(file_path)
        extension = path.suffix.lower()
        
        if extension not in self.formats:
            raise ValueError(f"Unsupported file format: {extension}")
            
        format_handler = self.formats[extension]
        return format_handler.read(file_path)
        
    def write_file(self, file_path: str, data: np.ndarray, metadata: Dict[str, Any] = None):
        """Write file with automatic format detection."""
        path = Path(file_path)
        extension = path.suffix.lower()
        
        if extension not in self.formats:
            raise ValueError(f"Unsupported file format: {extension}")
            
        format_handler = self.formats[extension]
        format_handler.write(file_path, data, metadata)
        
    def convert_format(self, input_path: str, output_path: str, metadata: Dict[str, Any] = None):
        """Convert between formats."""
        data, original_metadata = self.read_file(input_path)
        
        # Merge metadata
        if metadata:
            original_metadata.update(metadata)
            
        self.write_file(output_path, data, original_metadata)
        
    def get_supported_formats(self) -> List[str]:
        """Get list of supported formats."""
        return list(self.formats.keys())


# Global format manager
format_manager = FormatManager()
