#!/usr/bin/env python3
"""
Multi-Format Support Manager for AreTomo3 GUI
Comprehensive file format detection, validation, and conversion.
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod
import mimetypes

logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    import mrcfile
    MRC_AVAILABLE = True
except ImportError:
    MRC_AVAILABLE = False
    logger.warning("mrcfile not available - MRC support limited")

try:
    import tifffile
    TIFF_AVAILABLE = True
except ImportError:
    TIFF_AVAILABLE = False
    logger.warning("tifffile not available - TIFF support limited")

try:
    import h5py
    HDF5_AVAILABLE = True
except ImportError:
    HDF5_AVAILABLE = False
    logger.warning("h5py not available - HDF5 support limited")

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logger.warning("PIL not available - image format support limited")


@dataclass
class FormatInfo:
    """Information about a file format."""
    name: str
    extensions: List[str]
    mime_types: List[str]
    description: str
    is_supported: bool
    can_read: bool
    can_write: bool
    requires_library: Optional[str] = None


@dataclass
class FileMetadata:
    """Metadata extracted from a file."""
    file_path: str
    format_name: str
    file_size: int
    dimensions: Optional[Tuple[int, ...]] = None
    pixel_size: Optional[float] = None
    voltage: Optional[float] = None
    data_type: Optional[str] = None
    compression: Optional[str] = None
    additional_info: Dict[str, Any] = None


class FormatHandler(ABC):
    """Abstract base class for format handlers."""
    
    @abstractmethod
    def can_handle(self, file_path: Path) -> bool:
        """Check if this handler can process the file."""
        pass
    
    @abstractmethod
    def read_metadata(self, file_path: Path) -> FileMetadata:
        """Read metadata from the file."""
        pass
    
    @abstractmethod
    def validate_file(self, file_path: Path) -> Dict[str, Any]:
        """Validate file integrity and format compliance."""
        pass


class MRCHandler(FormatHandler):
    """Handler for MRC files."""
    
    def can_handle(self, file_path: Path) -> bool:
        return file_path.suffix.lower() in ['.mrc', '.mrcs', '.st', '.ali'] and MRC_AVAILABLE
    
    def read_metadata(self, file_path: Path) -> FileMetadata:
        """Read MRC file metadata."""
        if not MRC_AVAILABLE:
            raise ImportError("mrcfile library required for MRC support")
        
        try:
            with mrcfile.open(file_path, mode='r', permissive=True) as mrc:
                header = mrc.header
                
                metadata = FileMetadata(
                    file_path=str(file_path),
                    format_name="MRC",
                    file_size=file_path.stat().st_size,
                    dimensions=(int(header.nx), int(header.ny), int(header.nz)),
                    pixel_size=float(header.cella.x / header.nx) if header.nx > 0 else None,
                    voltage=float(header.voltage) if hasattr(header, 'voltage') else None,
                    data_type=str(mrc.data.dtype),
                    additional_info={
                        'mode': int(header.mode),
                        'min_value': float(header.dmin),
                        'max_value': float(header.dmax),
                        'mean_value': float(header.dmean),
                        'space_group': int(header.ispg),
                        'machine_stamp': header.machst.tobytes().decode('ascii', errors='ignore').strip(),
                        'labels': [label.decode('ascii', errors='ignore').strip() 
                                 for label in header.label if label.strip()]
                    }
                )
                
                return metadata
                
        except Exception as e:
            logger.error(f"Failed to read MRC metadata from {file_path}: {e}")
            raise
    
    def validate_file(self, file_path: Path) -> Dict[str, Any]:
        """Validate MRC file."""
        if not MRC_AVAILABLE:
            return {"valid": False, "error": "mrcfile library not available"}
        
        try:
            with mrcfile.open(file_path, mode='r', permissive=True) as mrc:
                # Basic validation
                header = mrc.header
                data = mrc.data
                
                validation_result = {
                    "valid": True,
                    "warnings": [],
                    "info": {
                        "header_valid": True,
                        "data_accessible": data is not None,
                        "dimensions_consistent": True,
                        "file_size_matches": True
                    }
                }
                
                # Check dimensions consistency
                expected_size = header.nx * header.ny * header.nz
                if data is not None and data.size != expected_size:
                    validation_result["warnings"].append("Data size doesn't match header dimensions")
                    validation_result["info"]["dimensions_consistent"] = False
                
                return validation_result
                
        except Exception as e:
            return {"valid": False, "error": str(e)}


class TIFFHandler(FormatHandler):
    """Handler for TIFF files."""
    
    def can_handle(self, file_path: Path) -> bool:
        return file_path.suffix.lower() in ['.tif', '.tiff'] and TIFF_AVAILABLE
    
    def read_metadata(self, file_path: Path) -> FileMetadata:
        """Read TIFF file metadata."""
        if not TIFF_AVAILABLE:
            raise ImportError("tifffile library required for TIFF support")
        
        try:
            with tifffile.TiffFile(file_path) as tif:
                page = tif.pages[0]
                
                metadata = FileMetadata(
                    file_path=str(file_path),
                    format_name="TIFF",
                    file_size=file_path.stat().st_size,
                    dimensions=page.shape,
                    data_type=str(page.dtype),
                    compression=page.compression.name if hasattr(page, 'compression') else None,
                    additional_info={
                        'page_count': len(tif.pages),
                        'photometric': page.photometric.name if hasattr(page, 'photometric') else None,
                        'planar_config': page.planarconfig.name if hasattr(page, 'planarconfig') else None,
                        'software': tif.pages[0].tags.get('Software', {}).value if 'Software' in tif.pages[0].tags else None
                    }
                )
                
                # Try to extract pixel size from tags
                if 'XResolution' in page.tags and 'YResolution' in page.tags:
                    x_res = page.tags['XResolution'].value
                    if isinstance(x_res, tuple) and len(x_res) == 2 and x_res[1] != 0:
                        metadata.pixel_size = x_res[1] / x_res[0]
                
                return metadata
                
        except Exception as e:
            logger.error(f"Failed to read TIFF metadata from {file_path}: {e}")
            raise
    
    def validate_file(self, file_path: Path) -> Dict[str, Any]:
        """Validate TIFF file."""
        if not TIFF_AVAILABLE:
            return {"valid": False, "error": "tifffile library not available"}
        
        try:
            with tifffile.TiffFile(file_path) as tif:
                validation_result = {
                    "valid": True,
                    "warnings": [],
                    "info": {
                        "page_count": len(tif.pages),
                        "all_pages_readable": True,
                        "consistent_dimensions": True
                    }
                }
                
                # Check if all pages are readable
                try:
                    for i, page in enumerate(tif.pages):
                        _ = page.shape  # Try to access page properties
                except Exception as e:
                    validation_result["warnings"].append(f"Page {i} not readable: {e}")
                    validation_result["info"]["all_pages_readable"] = False
                
                return validation_result
                
        except Exception as e:
            return {"valid": False, "error": str(e)}


class HDF5Handler(FormatHandler):
    """Handler for HDF5 files."""
    
    def can_handle(self, file_path: Path) -> bool:
        return file_path.suffix.lower() in ['.h5', '.hdf5', '.hdf'] and HDF5_AVAILABLE
    
    def read_metadata(self, file_path: Path) -> FileMetadata:
        """Read HDF5 file metadata."""
        if not HDF5_AVAILABLE:
            raise ImportError("h5py library required for HDF5 support")
        
        try:
            with h5py.File(file_path, 'r') as f:
                # Find the main dataset (largest one)
                main_dataset = None
                max_size = 0
                
                def find_datasets(name, obj):
                    nonlocal main_dataset, max_size
                    if isinstance(obj, h5py.Dataset):
                        if obj.size > max_size:
                            max_size = obj.size
                            main_dataset = obj
                
                f.visititems(find_datasets)
                
                metadata = FileMetadata(
                    file_path=str(file_path),
                    format_name="HDF5",
                    file_size=file_path.stat().st_size,
                    dimensions=main_dataset.shape if main_dataset else None,
                    data_type=str(main_dataset.dtype) if main_dataset else None,
                    compression=main_dataset.compression if main_dataset else None,
                    additional_info={
                        'dataset_count': len([key for key in f.keys()]),
                        'attributes': dict(f.attrs) if f.attrs else {},
                        'main_dataset_name': main_dataset.name if main_dataset else None
                    }
                )
                
                return metadata
                
        except Exception as e:
            logger.error(f"Failed to read HDF5 metadata from {file_path}: {e}")
            raise
    
    def validate_file(self, file_path: Path) -> Dict[str, Any]:
        """Validate HDF5 file."""
        if not HDF5_AVAILABLE:
            return {"valid": False, "error": "h5py library not available"}
        
        try:
            with h5py.File(file_path, 'r') as f:
                validation_result = {
                    "valid": True,
                    "warnings": [],
                    "info": {
                        "file_accessible": True,
                        "datasets_readable": True,
                        "no_corruption": True
                    }
                }
                
                # Try to access all datasets
                def check_dataset(name, obj):
                    if isinstance(obj, h5py.Dataset):
                        try:
                            _ = obj.shape  # Try to access dataset properties
                        except Exception as e:
                            validation_result["warnings"].append(f"Dataset {name} not accessible: {e}")
                            validation_result["info"]["datasets_readable"] = False
                
                f.visititems(check_dataset)
                
                return validation_result
                
        except Exception as e:
            return {"valid": False, "error": str(e)}


class FormatManager:
    """Comprehensive file format management system."""
    
    def __init__(self):
        self.handlers: List[FormatHandler] = []
        self.supported_formats: Dict[str, FormatInfo] = {}
        
        # Register handlers
        self._register_handlers()
        self._initialize_format_info()
        
        logger.info(f"Format manager initialized with {len(self.handlers)} handlers")
    
    def _register_handlers(self):
        """Register all available format handlers."""
        if MRC_AVAILABLE:
            self.handlers.append(MRCHandler())
        if TIFF_AVAILABLE:
            self.handlers.append(TIFFHandler())
        if HDF5_AVAILABLE:
            self.handlers.append(HDF5Handler())
    
    def _initialize_format_info(self):
        """Initialize supported format information."""
        formats = [
            FormatInfo(
                name="MRC",
                extensions=['.mrc', '.mrcs', '.st', '.ali'],
                mime_types=['application/octet-stream'],
                description="Medical Research Council format for electron microscopy",
                is_supported=MRC_AVAILABLE,
                can_read=MRC_AVAILABLE,
                can_write=MRC_AVAILABLE,
                requires_library="mrcfile"
            ),
            FormatInfo(
                name="TIFF",
                extensions=['.tif', '.tiff'],
                mime_types=['image/tiff'],
                description="Tagged Image File Format",
                is_supported=TIFF_AVAILABLE,
                can_read=TIFF_AVAILABLE,
                can_write=TIFF_AVAILABLE,
                requires_library="tifffile"
            ),
            FormatInfo(
                name="HDF5",
                extensions=['.h5', '.hdf5', '.hdf'],
                mime_types=['application/x-hdf'],
                description="Hierarchical Data Format version 5",
                is_supported=HDF5_AVAILABLE,
                can_read=HDF5_AVAILABLE,
                can_write=HDF5_AVAILABLE,
                requires_library="h5py"
            )
        ]
        
        for fmt in formats:
            self.supported_formats[fmt.name] = fmt
    
    def detect_format(self, file_path: Union[str, Path]) -> Optional[str]:
        """Detect file format based on extension and content."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            return None
        
        # Try each handler
        for handler in self.handlers:
            if handler.can_handle(file_path):
                try:
                    metadata = handler.read_metadata(file_path)
                    return metadata.format_name
                except Exception as e:
                    logger.warning(f"Handler failed for {file_path}: {e}")
                    continue
        
        return None
    
    def get_file_metadata(self, file_path: Union[str, Path]) -> Optional[FileMetadata]:
        """Get comprehensive metadata for a file."""
        file_path = Path(file_path)
        
        for handler in self.handlers:
            if handler.can_handle(file_path):
                try:
                    return handler.read_metadata(file_path)
                except Exception as e:
                    logger.error(f"Failed to read metadata for {file_path}: {e}")
                    continue
        
        return None
    
    def validate_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Validate a file's format and integrity."""
        file_path = Path(file_path)
        
        for handler in self.handlers:
            if handler.can_handle(file_path):
                return handler.validate_file(file_path)
        
        return {"valid": False, "error": "No handler available for this file format"}
    
    def get_supported_formats(self) -> Dict[str, FormatInfo]:
        """Get information about all supported formats."""
        return self.supported_formats.copy()
    
    def get_supported_extensions(self) -> List[str]:
        """Get list of all supported file extensions."""
        extensions = []
        for fmt in self.supported_formats.values():
            if fmt.is_supported:
                extensions.extend(fmt.extensions)
        return sorted(list(set(extensions)))
    
    def is_supported(self, file_path: Union[str, Path]) -> bool:
        """Check if a file format is supported."""
        file_path = Path(file_path)
        return any(handler.can_handle(file_path) for handler in self.handlers)
    
    def scan_directory(self, directory: Union[str, Path], 
                      recursive: bool = True) -> List[FileMetadata]:
        """Scan directory for supported files and return metadata."""
        directory = Path(directory)
        found_files = []
        
        pattern = "**/*" if recursive else "*"
        for file_path in directory.glob(pattern):
            if file_path.is_file() and self.is_supported(file_path):
                try:
                    metadata = self.get_file_metadata(file_path)
                    if metadata:
                        found_files.append(metadata)
                except Exception as e:
                    logger.warning(f"Failed to process {file_path}: {e}")
        
        return found_files
    
    def get_format_statistics(self, file_list: List[Union[str, Path]]) -> Dict[str, Any]:
        """Get statistics about file formats in a list."""
        stats = {
            "total_files": len(file_list),
            "supported_files": 0,
            "format_breakdown": {},
            "total_size": 0,
            "unsupported_files": []
        }
        
        for file_path in file_list:
            file_path = Path(file_path)
            if not file_path.exists():
                continue
                
            file_size = file_path.stat().st_size
            stats["total_size"] += file_size
            
            format_name = self.detect_format(file_path)
            if format_name:
                stats["supported_files"] += 1
                if format_name not in stats["format_breakdown"]:
                    stats["format_breakdown"][format_name] = {"count": 0, "size": 0}
                stats["format_breakdown"][format_name]["count"] += 1
                stats["format_breakdown"][format_name]["size"] += file_size
            else:
                stats["unsupported_files"].append(str(file_path))
        
        return stats
