CHANGELOG.md
INSTALLATION.md
LICENSE
MANIFEST.in
README.md
pyproject.toml
setup.cfg
setup.py
bin/aretomo3-gui
bin/aretomo3-gui.bat
scripts/install.py
scripts/verify_installation.py
src/aretomo3_gui/__init__.py
src/aretomo3_gui/__main__.py
src/aretomo3_gui/cli.py
src/aretomo3_gui/main.py
src/aretomo3_gui/qt_backend_init.py
src/aretomo3_gui.egg-info/PKG-INFO
src/aretomo3_gui.egg-info/SOURCES.txt
src/aretomo3_gui.egg-info/dependency_links.txt
src/aretomo3_gui.egg-info/entry_points.txt
src/aretomo3_gui.egg-info/not-zip-safe
src/aretomo3_gui.egg-info/requires.txt
src/aretomo3_gui.egg-info/top_level.txt
src/aretomo3_gui/analysis/__init__.py
src/aretomo3_gui/analysis/aretomo3_output_analyzer.py
src/aretomo3_gui/analysis/auto_plot_generator.py
src/aretomo3_gui/analysis/interactive_plots.py
src/aretomo3_gui/analysis/realtime_monitor.py
src/aretomo3_gui/analysis/ctf_analysis/__init__.py
src/aretomo3_gui/analysis/ctf_analysis/ctf_dashboard.py
src/aretomo3_gui/analysis/ctf_analysis/ctf_parser.py
src/aretomo3_gui/analysis/ctf_analysis/ctf_quality.py
src/aretomo3_gui/analysis/ctf_analysis/ctf_utils.py
src/aretomo3_gui/analysis/ctf_analysis/ctf_visualizer.py
src/aretomo3_gui/analysis/motion_analysis/__init__.py
src/aretomo3_gui/analysis/motion_analysis/motion_parser.py
src/aretomo3_gui/analysis/motion_analysis/motion_visualizer.py
src/aretomo3_gui/analytics/__init__.py
src/aretomo3_gui/analytics/advanced_analytics.py
src/aretomo3_gui/analytics/analyzer.py
src/aretomo3_gui/core/__init__.py
src/aretomo3_gui/core/advanced_logging.py
src/aretomo3_gui/core/backup_system.py
src/aretomo3_gui/core/config_manager.py
src/aretomo3_gui/core/continue_mode_manager.py
src/aretomo3_gui/core/data_validation.py
src/aretomo3_gui/core/database_manager.py
src/aretomo3_gui/core/dependency_check.py
src/aretomo3_gui/core/enhanced_database_manager.py
src/aretomo3_gui/core/enhanced_parameters.py
src/aretomo3_gui/core/enhanced_realtime_processor.py
src/aretomo3_gui/core/error_handling.py
src/aretomo3_gui/core/error_recovery.py
src/aretomo3_gui/core/file_organization.py
src/aretomo3_gui/core/file_watcher.py
src/aretomo3_gui/core/logging_config.py
src/aretomo3_gui/core/memory_manager.py
src/aretomo3_gui/core/multi_format_handler.py
src/aretomo3_gui/core/performance_monitor.py
src/aretomo3_gui/core/performance_optimizer.py
src/aretomo3_gui/core/plugin_system.py
src/aretomo3_gui/core/realtime_processor.py
src/aretomo3_gui/core/resource_manager.py
src/aretomo3_gui/core/results_tracker.py
src/aretomo3_gui/core/secure_web_api.py
src/aretomo3_gui/core/security_framework.py
src/aretomo3_gui/core/session_manager.py
src/aretomo3_gui/core/system_integration.py
src/aretomo3_gui/core/system_monitor.py
src/aretomo3_gui/core/thread_manager.py
src/aretomo3_gui/core/tilt_series.py
src/aretomo3_gui/core/automation/__init__.py
src/aretomo3_gui/core/automation/parameter_optimizer.py
src/aretomo3_gui/core/automation/quality_predictor.py
src/aretomo3_gui/core/automation/workflow_manager.py
src/aretomo3_gui/core/config/__init__.py
src/aretomo3_gui/core/config/config.py
src/aretomo3_gui/core/config/config_manager.py
src/aretomo3_gui/core/config/config_validation.py
src/aretomo3_gui/core/config/template_manager.py
src/aretomo3_gui/core/config/.cache/window_state.json
src/aretomo3_gui/data_management/__init__.py
src/aretomo3_gui/data_management/data_manager.py
src/aretomo3_gui/data_management/manager.py
src/aretomo3_gui/formats/__init__.py
src/aretomo3_gui/formats/format_manager.py
src/aretomo3_gui/formats/manager.py
src/aretomo3_gui/gui/__init__.py
src/aretomo3_gui/gui/advanced_settings_tab.py
src/aretomo3_gui/gui/main_window.py
src/aretomo3_gui/gui/minimal_gui.py
src/aretomo3_gui/gui/plot_theme_manager.py
src/aretomo3_gui/gui/rich_main_window.py
src/aretomo3_gui/gui/theme_manager.py
src/aretomo3_gui/gui/themes.py
src/aretomo3_gui/gui/analysis/__init__.py
src/aretomo3_gui/gui/analysis/real_time_analyzer.py
src/aretomo3_gui/gui/components/__init__.py
src/aretomo3_gui/gui/components/menu_manager.py
src/aretomo3_gui/gui/components/napari_viewer.py
src/aretomo3_gui/gui/components/parameter_manager.py
src/aretomo3_gui/gui/components/session_manager.py
src/aretomo3_gui/gui/embedded_viewers/__init__.py
src/aretomo3_gui/gui/embedded_viewers/ctf_viewer.py
src/aretomo3_gui/gui/embedded_viewers/motion_viewer.py
src/aretomo3_gui/gui/tabs/__init__.py
src/aretomo3_gui/gui/tabs/analysis_tab.py
src/aretomo3_gui/gui/tabs/batch_tab.py
src/aretomo3_gui/gui/tabs/ctf_tab.py
src/aretomo3_gui/gui/tabs/enhanced_analysis_tab.py
src/aretomo3_gui/gui/tabs/enhanced_monitor_tab.py
src/aretomo3_gui/gui/tabs/enhanced_parameters_tab.py
src/aretomo3_gui/gui/tabs/export_tab.py
src/aretomo3_gui/gui/tabs/live_processing_tab.py
src/aretomo3_gui/gui/tabs/log_tab.py
src/aretomo3_gui/gui/tabs/main_tab.py
src/aretomo3_gui/gui/tabs/monitor_tab.py
src/aretomo3_gui/gui/tabs/napari_viewer_tab.py
src/aretomo3_gui/gui/tabs/realtime_analysis_tab.py
src/aretomo3_gui/gui/tabs/reorganized_main_tab.py
src/aretomo3_gui/gui/tabs/reorganized_main_tab_backup.py
src/aretomo3_gui/gui/tabs/unified_analysis_tab.py
src/aretomo3_gui/gui/tabs/unified_live_processing_tab.py
src/aretomo3_gui/gui/tabs/viewer_tab.py
src/aretomo3_gui/gui/tabs/web_dashboard_tab.py
src/aretomo3_gui/gui/viewers/__init__.py
src/aretomo3_gui/gui/viewers/analysis_viewer.py
src/aretomo3_gui/gui/viewers/mock_napari_viewer.py
src/aretomo3_gui/gui/viewers/mrc_viewer.py
src/aretomo3_gui/gui/viewers/napari_mrc_viewer.py
src/aretomo3_gui/gui/viewers/preview_grid.py
src/aretomo3_gui/gui/viewers/visualization.py
src/aretomo3_gui/gui/visualizers/motion_correction_visualizer.py
src/aretomo3_gui/gui/widgets/__init__.py
src/aretomo3_gui/gui/widgets/advanced_file_browser.py
src/aretomo3_gui/gui/widgets/advanced_progress_widget.py
src/aretomo3_gui/gui/widgets/batch_processing.py
src/aretomo3_gui/gui/widgets/enhanced_progress_visualization.py
src/aretomo3_gui/gui/widgets/enhanced_spinbox.py
src/aretomo3_gui/gui/widgets/gpu_manager_widget.py
src/aretomo3_gui/gui/widgets/live_tilt_series_monitor.py
src/aretomo3_gui/gui/widgets/multigpu_manager.py
src/aretomo3_gui/gui/widgets/processing_dashboard.py
src/aretomo3_gui/gui/widgets/project_management.py
src/aretomo3_gui/gui/widgets/realtime_widget.py
src/aretomo3_gui/gui/widgets/resource_monitor.py
src/aretomo3_gui/gui/widgets/smart_file_organizer.py
src/aretomo3_gui/gui/widgets/unified_processing_monitor.py
src/aretomo3_gui/gui/widgets/web_server_widget.py
src/aretomo3_gui/integration/__init__.py
src/aretomo3_gui/integration/external_tools.py
src/aretomo3_gui/integration/manager.py
src/aretomo3_gui/particle_picking/__init__.py
src/aretomo3_gui/particle_picking/picker.py
src/aretomo3_gui/realtime/__init__.py
src/aretomo3_gui/realtime/processor.py
src/aretomo3_gui/subtomogram/__init__.py
src/aretomo3_gui/subtomogram/averaging.py
src/aretomo3_gui/tools/__init__.py
src/aretomo3_gui/tools/kmeans_integration.py
src/aretomo3_gui/utils/__init__.py
src/aretomo3_gui/utils/aretomo3_parser.py
src/aretomo3_gui/utils/documentation_generator.py
src/aretomo3_gui/utils/eer_reader.py
src/aretomo3_gui/utils/eer_reader_new.py
src/aretomo3_gui/utils/export_functions.py
src/aretomo3_gui/utils/file_utils.py
src/aretomo3_gui/utils/mdoc_parser.py
src/aretomo3_gui/utils/pdf_report_generator.py
src/aretomo3_gui/utils/utils.py
src/aretomo3_gui/utils/warning_suppression.py
src/aretomo3_gui/visualization/__init__.py
src/aretomo3_gui/visualization/engine.py
src/aretomo3_gui/web/__init__.py
src/aretomo3_gui/web/api_server.py
src/aretomo3_gui/web/plot_server.py
src/aretomo3_gui/web/server.py
src/aretomo3_gui/web/templates/analysis_dashboard.html
src/aretomo3_gui/web_interface/__init__.py
src/aretomo3_gui/web_interface/server.py
src/aretomo3_gui/workflow/__init__.py
src/aretomo3_gui/workflow/engine.py