# MANIFEST.in for AreTomo3 GUI Professional
# Ensures all necessary files are included in the distribution packages

# Include all Python source files
recursive-include src/aretomo3_gui *.py
recursive-include src/aretomo3_gui *.pyx
recursive-include src/aretomo3_gui *.pxd

# Include data files and resources
recursive-include src/aretomo3_gui *.json
recursive-include src/aretomo3_gui *.yaml
recursive-include src/aretomo3_gui *.yml
recursive-include src/aretomo3_gui *.txt
recursive-include src/aretomo3_gui *.md
recursive-include src/aretomo3_gui *.rst
recursive-include src/aretomo3_gui *.html
recursive-include src/aretomo3_gui *.css
recursive-include src/aretomo3_gui *.js
recursive-include src/aretomo3_gui *.png
recursive-include src/aretomo3_gui *.jpg
recursive-include src/aretomo3_gui *.jpeg
recursive-include src/aretomo3_gui *.gif
recursive-include src/aretomo3_gui *.svg
recursive-include src/aretomo3_gui *.ico

# Include configuration files
recursive-include config *.json
recursive-include config *.yaml
recursive-include config *.yml
recursive-include config *.ini
recursive-include config *.cfg
recursive-include config *.conf

# Include documentation
include README.md
include LICENSE
include CHANGELOG.md
include INSTALLATION.md
include COMPREHENSIVE_TEST_RESULTS.md
include FINAL_DISTRIBUTION_SUMMARY.md
include FINAL_PACKAGE_SUMMARY.md
include DEPLOYMENT_INFO.json
include TEST_REPORT.json

# Include scripts
recursive-include scripts *.py
recursive-include scripts *.sh
recursive-include scripts *.bat

# Include binary files
recursive-include bin *

# Include examples
recursive-include examples *.py
recursive-include examples *.json
recursive-include examples *.yaml
recursive-include examples *.yml
recursive-include examples *.txt
recursive-include examples *.md

# Include test files
recursive-include tests *.py

# Include docs
recursive-include docs *.md
recursive-include docs *.rst
recursive-include docs *.txt
recursive-include docs *.html
recursive-include docs *.css
recursive-include docs *.js
recursive-include docs *.png
recursive-include docs *.jpg
recursive-include docs *.svg

# Exclude unwanted files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .DS_Store
global-exclude *.so
global-exclude *.dylib
global-exclude *.dll
global-exclude .pytest_cache
global-exclude .coverage
global-exclude .tox
global-exclude .venv
global-exclude venv
global-exclude env
global-exclude build
global-exclude dist
global-exclude *.egg-info
