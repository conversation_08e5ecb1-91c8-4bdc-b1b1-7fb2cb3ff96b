#!/usr/bin/env python3
"""
AreTomo3 GUI Professional Setup Script
Production-ready installation setup with comprehensive dependency management and testing.
"""

from setuptools import setup, find_packages
from pathlib import Path
import json

# Read version and metadata
def read_deployment_info():
    """Read deployment information."""
    try:
        with open("DEPLOYMENT_INFO.json") as f:
            return json.load(f)
    except FileNotFoundError:
        return {
            "name": "aretomo3-gui-professional",
            "version": "3.0.0",
            "description": "Professional AreTomo3 GUI - Production Ready with Comprehensive Testing"
        }

# Read requirements
def read_requirements(filename):
    """Read requirements from file."""
    req_file = Path("requirements") / filename
    if req_file.exists():
        with open(req_file) as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    return []

# Read long description
def read_long_description():
    """Read long description from README."""
    readme_file = Path("README.md")
    if readme_file.exists():
        with open(readme_file, encoding="utf-8") as f:
            return f.read()
    return "Professional AreTomo3 GUI with comprehensive features"

# Get deployment info
deployment_info = read_deployment_info()

# Core requirements
install_requires = read_requirements("requirements.txt")

# Optional requirements
extras_require = {
    "web": ["Flask>=2.0.0", "Flask-CORS>=3.0.0"],
    "analytics": ["scikit-learn>=1.0.0", "scikit-image>=0.19.0"],
    "visualization": ["napari>=0.4.15"],
    "formats": ["PyYAML>=6.0", "h5py>=3.6.0", "mrcfile>=1.3.0"],
    "performance": ["numba>=0.56.0"],
    "database": ["sqlalchemy>=1.4.0"],
    "dev": read_requirements("requirements-dev.txt")
}

# All optional dependencies
extras_require["all"] = list(set(
    dep for deps in extras_require.values() for dep in deps
    if not dep.startswith("-r")
))

setup(
    # Basic package information
    name=deployment_info["name"],
    version=deployment_info["version"],
    description=deployment_info["description"],
    long_description=read_long_description(),
    long_description_content_type="text/markdown",
    
    # Author information
    author="AreTomo3 GUI Development Team",
    author_email="<EMAIL>",
    url="https://github.com/aretomo3-gui/aretomo3-gui-professional",
    
    # Package discovery
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    
    # Dependencies
    python_requires=">=3.8",
    install_requires=install_requires,
    extras_require=extras_require,
    
    # Entry points
    entry_points={
        "console_scripts": [
            "aretomo3-gui=aretomo3_gui.main:main",
            "aretomo3-gui-cli=aretomo3_gui.cli:main",
        ],
    },
    
    # Package data
    include_package_data=True,
    package_data={
        "aretomo3_gui": [
            "config/*.json",
            "config/*.conf",
            "data/templates/*",
            "data/sample_data/*",
        ],
    },
    
    # Classifiers
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Science/Research",
        "Topic :: Scientific/Engineering :: Bio-Informatics",
        "Topic :: Scientific/Engineering :: Image Processing",
        "Topic :: Scientific/Engineering :: Visualization",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
        "Environment :: X11 Applications :: Qt",
        "Environment :: Web Environment",
        "Environment :: GPU :: NVIDIA CUDA",
    ],
    
    # Keywords
    keywords=[
        "cryo-electron tomography",
        "image processing",
        "scientific computing",
        "gui",
        "aretomo3",
        "tomography reconstruction"
    ],
    
    # Project URLs
    project_urls={
        "Documentation": "https://aretomo3-gui.readthedocs.io/",
        "Source": "https://github.com/aretomo3-gui/aretomo3-gui-professional",
        "Tracker": "https://github.com/aretomo3-gui/aretomo3-gui-professional/issues",
    },
    
    # Additional metadata
    zip_safe=False,
    platforms=["any"],
)
