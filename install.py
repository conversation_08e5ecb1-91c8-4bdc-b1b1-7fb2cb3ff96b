#!/usr/bin/env python3
'''
AreTomo3 GUI v3.0.1 Installation Script
Monitor Tab Fix Release
'''

import subprocess
import sys
import os

def install_aretomo3_gui():
    '''Install AreTomo3 GUI from wheel package.'''
    
    print("🔧 AreTomo3 GUI v3.0.1 - Monitor Tab Fix Release")
    print("=" * 60)
    
    # Check if wheel package exists
    wheel_file = "dist/aretomo3_gui-3.0.1-py3-none-any.whl"
    if not os.path.exists(wheel_file):
        print(f"❌ Wheel package not found: {wheel_file}")
        return False
    
    try:
        # Install the package
        print(f"📦 Installing {wheel_file}...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", wheel_file
        ], check=True)
        
        print("✅ Installation completed successfully!")
        
        # Verify installation
        print("🧪 Verifying installation...")
        result = subprocess.run([
            sys.executable, "-c", "import aretomo3_gui; print(f'AreTomo3 GUI v{aretomo3_gui.__version__} installed successfully')"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {result.stdout.strip()}")
            print("\n🎉 AreTomo3 GUI is ready to use!")
            print("\n📋 What's Fixed in v3.0.1:")
            print("  ✅ Configuration display updates when processing starts")
            print("  ✅ Processing status shows real-time updates with timestamps")
            print("  ✅ Monitor tab properly connected to processing events")
            print("  ✅ Fixed datetime import issues")
            print("\n🚀 Launch with: aretomo3-gui")
            return True
        else:
            print(f"❌ Verification failed: {result.stderr}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        return False

if __name__ == "__main__":
    success = install_aretomo3_gui()
    sys.exit(0 if success else 1)
