#!/usr/bin/env python3
"""
AreTomo3 GUI Professional Installation Script
Automated installation with dependency management and verification.
"""

import sys
import subprocess
import os
from pathlib import Path

def main():
    print("🚀 AreTomo3 GUI Professional Installation")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        sys.exit(1)
    
    print("✅ Python version check passed")
    
    # Install package
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-e", "."], check=True)
        print("✅ Package installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Installation failed")
        sys.exit(1)
    
    # Run verification
    try:
        subprocess.run([sys.executable, "scripts/verify_installation.py"], check=True)
        print("✅ Installation verified")
    except subprocess.CalledProcessError:
        print("⚠️  Installation verification had issues")
    
    print("\n🎉 Installation complete!")
    print("Run 'aretomo3-gui' to start the application")

if __name__ == "__main__":
    main()
