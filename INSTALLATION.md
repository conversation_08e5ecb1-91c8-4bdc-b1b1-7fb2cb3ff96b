# AreTomo3 GUI Professional - Installation Guide

## 🚀 Quick Start Installation

### Automated Installation (Recommended)

1. **Extract the Distribution Package**
   ```bash
   unzip AreTomo3-GUI-Professional-v3.0.0-*.zip
   cd AreTomo3-GUI-Professional-v3.0.0/
   ```

2. **Run the Automated Installer**
   ```bash
   python scripts/install.py
   ```

3. **Launch the Application**
   ```bash
   aretomo3-gui
   ```

---

## 📋 System Requirements

### Minimum Requirements
- **Python:** 3.8 or higher
- **Operating System:** Windows 10+, macOS 10.14+, or Linux
- **Memory:** 2GB RAM
- **Storage:** 500MB free space
- **Display:** Required for GUI components

### Recommended Requirements
- **Python:** 3.10+ (tested with 3.12.9)
- **Memory:** 4GB+ RAM (for large datasets)
- **Storage:** 1GB+ free space
- **GPU:** CUDA-compatible GPU for acceleration (optional)

---

## 🛠️ Installation Methods

### Method 1: Automated Installation (Recommended)

The automated installer handles all dependencies and configuration:

```bash
# Navigate to extracted directory
cd AreTomo3-GUI-Professional-v3.0.0/

# Run installer
python scripts/install.py

# Verify installation
python scripts/verify_installation.py
```

### Method 2: Wheel Package Installation

Install from the pre-built wheel package:

```bash
pip install dist/aretomo3_gui-2.0.0-py3-none-any.whl
```

### Method 3: Development Installation

For development or customization:

```bash
# Install in editable mode
pip install -e .

# Or using setup.py
python setup.py develop
```

### Method 4: Manual Installation

Install dependencies manually:

```bash
# Install core dependencies
pip install PyQt6 PyQt6-WebEngine numpy scipy pandas matplotlib

# Install optional dependencies
pip install napari[pyqt6] fastapi uvicorn flask

# Install the package
pip install -e .
```

---

## ✅ Verification

### Quick Verification
```bash
# Check version
aretomo3-gui --version

# Run verification script
python scripts/verify_installation.py
```

### Comprehensive Testing
```bash
# Run comprehensive tests (if available)
python comprehensive_test_and_package.py

# Run final verification
python FINAL_VERIFICATION.py
```

---

## 🚀 Usage

### Basic Usage
```bash
# Launch GUI application
aretomo3-gui

# Launch with debug mode
aretomo3-gui --debug

# Show help
aretomo3-gui --help
```

### Web Interface (Optional)
```bash
# Start web server
aretomo3-gui-web

# Access at: http://localhost:8000
```

### Command Line Interface
```bash
# Use CLI mode
python -m aretomo3_gui
```

---

## 🔧 Troubleshooting

### Common Issues

#### Python Version Issues
```bash
# Check Python version
python --version

# Use specific Python version if needed
python3.10 scripts/install.py
```

#### Permission Issues (Linux/macOS)
```bash
# Install with user permissions
pip install --user -e .

# Or use sudo (not recommended)
sudo python scripts/install.py
```

#### Missing Dependencies
```bash
# Update pip first
pip install --upgrade pip

# Install build tools
pip install build wheel setuptools

# Retry installation
python scripts/install.py
```

#### Qt/GUI Issues
```bash
# Install Qt dependencies (Linux)
sudo apt-get install python3-pyqt6 python3-pyqt6.qtwebengine

# For headless systems, install virtual display
sudo apt-get install xvfb
export DISPLAY=:99
Xvfb :99 -screen 0 1024x768x24 &
```

### Environment-Specific Issues

#### Conda Environments
```bash
# Create new conda environment
conda create -n aretomo3-gui python=3.10
conda activate aretomo3-gui

# Install in conda environment
python scripts/install.py
```

#### Virtual Environments
```bash
# Create virtual environment
python -m venv aretomo3-env
source aretomo3-env/bin/activate  # Linux/macOS
# or
aretomo3-env\Scripts\activate     # Windows

# Install in virtual environment
python scripts/install.py
```

---

## 📖 Additional Resources

### Documentation
- **README.md** - Main project documentation
- **COMPREHENSIVE_TEST_RESULTS.md** - Detailed testing information
- **FINAL_DISTRIBUTION_SUMMARY.md** - Distribution summary
- **TEST_REPORT.json** - Technical test metrics

### Support
- Check the `docs/` directory for additional documentation
- Review test results for troubleshooting information
- Ensure all system requirements are met

---

## 🎯 Success Indicators

After successful installation, you should see:

1. ✅ No error messages during installation
2. ✅ `aretomo3-gui --version` shows version information
3. ✅ All verification tests pass
4. ✅ Application launches without errors

---

**Installation Guide for AreTomo3 GUI Professional v3.0.0**  
*Production-Ready • Comprehensively Tested • Ready for Distribution*
